const { app } = require('@azure/functions');
const { jsonResponse } = require('../common/helper');
const { HttpStatusCode } = require('axios');
const lifestyleAmbientHandler = require('../handlers/lifestyle-ambient-handler');

app.http('lifestyle-ambient-listening', {
    methods: ['POST'],
    authLevel: 'function',
    route: 'lifestyle/ambient-listening',
    handler: async (req, context) => {
        context.log(`Http function processed request for url "${req.url}"`);

        try {
            const body = await req.json();
            const { transcript, source } = body;

            if (!transcript || transcript.trim() === '') {
                return jsonResponse('Missing or empty transcript', HttpStatusCode.BadRequest);
            }

            if (!source || source.trim() === '') {
                return jsonResponse('Missing or empty source', HttpStatusCode.BadRequest);
            }

            context.log(`Processing lifestyle ambient listening for source: ${source}`);

            const result = await lifestyleAmbientHandler.processLifestyleAmbientListening(transcript, source);

            if (!result) {
                return jsonResponse('Failed to process lifestyle ambient listening', HttpStatusCode.InternalServerError);
            }

            return jsonResponse({
                conversation: result.conversation || [],
                summary: result.summary || {}
            }, HttpStatusCode.Ok);

        } catch (error) {
            context.log('Error in lifestyle ambient listening handler:', error);
            
            if (error.name === 'SyntaxError') {
                return jsonResponse('Invalid JSON in request body', HttpStatusCode.BadRequest);
            }
            
            return jsonResponse('Internal server error', HttpStatusCode.InternalServerError);
        }
    }
});
