const { app } = require('@azure/functions')
const {
  BlobServiceClient,
  generateBlobSASQueryParameters,
  BlobSASPermissions,
  StorageSharedKeyCredential,
} = require('@azure/storage-blob')
const { encryptBuffer } = require('../services/encryptionService')
const {
  saveLabReportMetadata,
  updateLabReportMetadata,
} = require('../services/cosmosService')
const { v4: uuidv4 } = require('uuid')
const axios = require('axios')
const { getLabReportMetadata } = require('../services/cosmosService')
const { decryptBuffer } = require('../services/encryptionService')
const { fileTypeFromBuffer } = require('file-type')
const labTestService = require('../services/patient-lab-test-service')
const { LabTestStatus } = require('../common/constant')
const FormData = require('form-data')

const OCR_SERVICE_URL =
  'http://ocrcontainergroup-v1.eastus.azurecontainer.io:8000/ocr/'

// Function to send document to OCR service
async function processDocumentWithOCR(buffer, fileName, context) {
  try {
    context.log('Starting OCR processing for file:', fileName)

    const formData = new FormData()
    formData.append('file', buffer, {
      filename: fileName,
      contentType: 'application/pdf',
    })

    const response = await axios.post(OCR_SERVICE_URL, formData, {
      headers: {
        ...formData.getHeaders(),
        Accept: '*/*',
        'Accept-Language': 'en-GB,en-US;q=0.9,en;q=0.8',
        Connection: 'keep-alive',
        'User-Agent':
          'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36',
      },
      timeout: 120000,
    })

    context.log('OCR processing completed successfully')
    return response.data
  } catch (error) {
    context.log.error('OCR processing failed:', error.message)
    throw error
  }
}

function calculateStringSimilarity(str1, str2) {
  const len1 = str1.length
  const len2 = str2.length

  if (len1 === 0) return len2 === 0 ? 1 : 0
  if (len2 === 0) return 0

  const matrix = Array(len1 + 1)
    .fill()
    .map(() => Array(len2 + 1).fill(0))

  for (let i = 0; i <= len1; i++) matrix[i][0] = i
  for (let j = 0; j <= len2; j++) matrix[0][j] = j

  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      const cost = str1[i - 1] === str2[j - 1] ? 0 : 1
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1,
        matrix[i][j - 1] + 1,
        matrix[i - 1][j - 1] + cost,
      )
    }
  }

  const maxLen = Math.max(len1, len2)
  return (maxLen - matrix[len1][len2]) / maxLen
}

async function updateLabTestsWithOCRResults(labTestId, ocrData, context) {
  try {
    context.log('Updating lab tests with OCR results for labTestId:', labTestId)

    const labTest = await labTestService.getLabTestById(labTestId)
    if (!labTest || labTest.length === 0) {
      context.log.error('Lab test not found:', labTestId)
      return false
    }

    const labTestRecord = labTest[0]

    const ocrTestResults = ocrData.structured?.test_results || []

    // Create a copy of used OCR results to avoid duplicate matching
    const availableOCRResults = [...ocrTestResults]

    labTestRecord.labTests = labTestRecord.labTests.map((test) => {
      console.log(`Processing test: ${test.testName?.toLowerCase()}`)

      const matchingIndex = availableOCRResults.findIndex((ocrTest) => {
        const testName = test.testName?.toLowerCase().trim() || ''

        let ocrTestName = ''
        let pureOcrTestName = ''
        if (ocrTest['Test Name']) {
          ocrTestName = ocrTest['Test Name'].toLowerCase().trim()
        } else if (ocrTest.test_name) {
          ocrTestName = ocrTest.test_name.toLowerCase().trim()

          if (ocrTest.unit && ocrTest.unit.trim()) {
            pureOcrTestName = ocrTestName
            ocrTestName += ` [${ocrTest.unit.trim()}]`
          }
        }

        if (testName === ocrTestName || pureOcrTestName === testName) {
          return true
        }

        const normalizeString = (str) => {
          return str
            .replace(/[|]/g, 'i')
            .replace(/[1]/g, 'i')
            .replace(/[0]/g, 'o')
            .replace(/[-_]/g, ' ')
            .replace(/\s+/g, ' ')
            .replace(/[^\w\s]/g, '')
            .trim()
        }

        const normalizedTestName = normalizeString(testName)
        const normalizedOCRName = normalizeString(ocrTestName)

        if (normalizedTestName === normalizedOCRName) {
          return true
        }

        if (testName.length > 8 && ocrTestName.length > 8) {
          const similarity = calculateStringSimilarity(
            normalizedTestName,
            normalizedOCRName,
          )

          if (similarity >= 0.9) {
            return true
          }
        }

        return false
      })

      if (matchingIndex !== -1) {
        const matchingOCRTest = availableOCRResults[matchingIndex]

        availableOCRResults.splice(matchingIndex, 1)

        // const ocrValue =
        // (matchingOCRTest.Value || matchingOCRTest.value || '') +
        // (matchingOCRTest.unit || '')
        const ocrValue = matchingOCRTest.Value || matchingOCRTest.value || ''
        const ocrReference =
          matchingOCRTest['Reference Interval'] ||
          matchingOCRTest.reference_interval ||
          ''

        let ocrTestName = ''
        if (matchingOCRTest['Test Name']) {
          ocrTestName = matchingOCRTest['Test Name']
        } else if (matchingOCRTest.test_name) {
          ocrTestName = matchingOCRTest.test_name
          if (matchingOCRTest.unit && matchingOCRTest.unit.trim()) {
            ocrTestName += ` [${matchingOCRTest.unit.trim()}]`
          }
        }

        return {
          ...test,
          results: ocrValue || test.results,
          reference: ocrReference || test.reference,
          status: LabTestStatus.READY,
        }
      }

      return test
    })

    await labTestService.updateLabTest(labTestRecord)
    context.log('Lab test updated successfully with OCR results')
    return true
  } catch (error) {
    context.log.error(
      'Failed to update lab tests with OCR results:',
      error.message,
    )
    return false
  }
}

app.http('lab-report-upload', {
  methods: ['POST'],
  authLevel: 'function',
  route: 'lab-report/upload',
  handler: async (req, context) => {
    try {
      context.log('Environment check:', {
        hasStorage: !!process.env.AzureWebJobsStorage,
        hasAccountName: !!process.env.AZURE_STORAGE_ACCOUNT_NAME,
        hasAccountKey: !!process.env.AZURE_STORAGE_ACCOUNT_KEY,
        nodeVersion: process.version,
        platform: process.platform,
      })

      const formData = await req.formData()
      const files = formData.getAll('files')
      const patientId = formData.get('patientId')
      const labTestId = formData.get('labTestId')

      if (!files || files.length === 0 || !patientId || !labTestId) {
        return {
          status: 400,
          jsonBody: { error: 'Missing files, patientId, or labTestId' },
        }
      }

      const uploadedMetadata = []

      for (const file of files) {
        if (!(file instanceof File) && !(file instanceof Blob)) {
          return {
            status: 400,
            jsonBody: { error: 'Invalid file object received' },
          }
        }

        if (!file.size || file.size === 0) {
          return {
            status: 400,
            jsonBody: { error: 'Empty file not allowed' },
          }
        }
        async function safeArrayBuffer(file, retries = 3) {
          for (let i = 0; i < retries; i++) {
            try {
              context.log(`ArrayBuffer attempt ${i + 1}`)

              const arrayBuffer = await file.arrayBuffer()

              if (!arrayBuffer) {
                throw new Error('ArrayBuffer is null or undefined')
              }

              if (arrayBuffer.byteLength === 0) {
                throw new Error('ArrayBuffer is empty')
              }

              context.log(
                `ArrayBuffer success: ${arrayBuffer.byteLength} bytes`,
              )
              return arrayBuffer
            } catch (error) {
              context.log(`ArrayBuffer attempt ${i + 1} failed:`, error.message)

              if (i === retries - 1) {
                throw new Error(
                  `Failed to read file after ${retries} attempts: ${error.message}`,
                )
              }

              await new Promise((resolve) => setTimeout(resolve, 100 * (i + 1)))
            }
          }
        }

        async function fallbackFileRead(file) {
          context.log('Using fallback file reading method')

          const chunks = []
          const reader = file.stream().getReader()

          try {
            while (true) {
              const { done, value } = await reader.read()
              if (done) break
              chunks.push(value)
            }

            const totalLength = chunks.reduce(
              (acc, chunk) => acc + chunk.length,
              0,
            )
            const buffer = Buffer.concat(
              chunks.map((chunk) => Buffer.from(chunk)),
              totalLength,
            )

            context.log(`Fallback read success: ${buffer.length} bytes`)
            return buffer
          } catch (streamError) {
            throw new Error(
              `Fallback file reading failed: ${streamError.message}`,
            )
          } finally {
            reader.releaseLock()
          }
        }
        let buffer
        try {
          const arrayBuffer = await safeArrayBuffer(file)
          buffer = Buffer.from(arrayBuffer)
        } catch (arrayBufferError) {
          context.log(
            'ArrayBuffer method failed, trying fallback:',
            arrayBufferError.message,
          )
          buffer = await fallbackFileRead(file)
        }

        if (!buffer || buffer.length === 0) {
          return {
            status: 400,
            jsonBody: { error: 'File buffer is empty or invalid' },
          }
        }

        // Encrypt the buffer
        const { encryptedData, encryptionKey, iv } = await encryptBuffer(buffer)
        const fileId = uuidv4()
        const blobName = `patients/${patientId}/labtest/${labTestId}/${fileId}-${file.name}`

        // Azure Blob Storage setup
        const connectionString = process.env.AzureWebJobsStorage || ''
        const containerName = 'lab-reports'

        const blobServiceClient =
          BlobServiceClient.fromConnectionString(connectionString)
        const containerClient =
          blobServiceClient.getContainerClient(containerName)
        await containerClient.createIfNotExists()

        // Generate SAS URL for upload
        const sharedKeyCredential = new StorageSharedKeyCredential(
          process.env.AZURE_STORAGE_ACCOUNT_NAME,
          process.env.AZURE_STORAGE_ACCOUNT_KEY,
        )

        const sasToken = generateBlobSASQueryParameters(
          {
            containerName,
            blobName,
            permissions: BlobSASPermissions.parse('cw'),
            expiresOn: new Date(Date.now() + 10 * 60 * 1000),
          },
          sharedKeyCredential,
        ).toString()

        const uploadUrl = `https://${process.env.AZURE_STORAGE_ACCOUNT_NAME}.blob.core.windows.net/${containerName}/${blobName}?${sasToken}`

        await axios.put(uploadUrl, encryptedData, {
          headers: {
            'x-ms-blob-type': 'BlockBlob',
            'Content-Length': encryptedData.length,
          },
          timeout: 30000,
        })

        const metadata = await saveLabReportMetadata({
          id: fileId,
          fileName: file.name,
          fileSize: file.size,
          blobPath: blobName,
          encryptionKey,
          iv,
          patientId,
          labTestId,
          ocrStatus: 'pending',
          detectedLanguage: null,
          ocrData: null,
          fileType: file.type,
          uploadedAt: new Date().toISOString(),
        })

        uploadedMetadata.push(metadata)

        try {
          context.log('Starting OCR processing for file:', file.name)

          await updateLabReportMetadata({
            ...metadata,
            ocrStatus: 'processing',
          })

          const ocrData = await processDocumentWithOCR(
            buffer,
            file.name,
            context,
          )

          await updateLabReportMetadata({
            ...metadata,
            ocrStatus: 'completed',
            ocrData: ocrData,
          })

          context.log(
            'OCR processing completed successfully for file:',
            file.name,
          )
        } catch (ocrError) {
          context.log.error(
            'OCR processing failed for file:',
            file.name,
            ocrError.message,
          )

          await updateLabReportMetadata({
            ...metadata,
            ocrStatus: 'failed',
            ocrData: null,
          })
        }
      }

      const labTest = await labTestService.getLabTestById(labTestId)
      if (labTest && labTest.length > 0) {
        labTest[0].labTests = labTest[0].labTests.map((test) => {
          return {
            ...test,
            status: LabTestStatus.UPLOADED,
            fileMetadata: uploadedMetadata.filter(
              (meta) => meta.labTestId === test.id,
            ),
          }
        })
        await labTestService.updateLabTest(labTest[0])
      }

      try {
        context.log('Processing OCR results for lab tests')

        const completedOCRFiles = uploadedMetadata

        for (const metadata of completedOCRFiles) {
          try {
            const latestMetadata = await getLabReportMetadata(metadata.id)

            if (
              latestMetadata &&
              latestMetadata.ocrStatus === 'completed' &&
              latestMetadata.ocrData
            ) {
              context.log(
                'Processing OCR results for file:',
                latestMetadata.fileName,
              )

              const updateSuccess = await updateLabTestsWithOCRResults(
                labTestId,
                latestMetadata.ocrData,
                context,
              )

              if (updateSuccess) {
                context.log(
                  'Successfully updated lab tests with OCR results for file:',
                  latestMetadata.fileName,
                )
              } else {
                context.log(
                  'Failed to update lab tests with OCR results for file:',
                  latestMetadata.fileName,
                )
              }
            } else {
              context.log(
                'OCR not completed or no data available for file:',
                metadata.fileName,
              )
            }
          } catch (ocrProcessError) {
            context.log.error(
              'Error processing OCR results for file:',
              metadata.fileName,
              ocrProcessError.message,
            )
          }
        }
      } catch (ocrProcessingError) {
        context.log.error(
          'Error in OCR results processing:',
          ocrProcessingError.message,
        )
      }

      context.log('Upload completed successfully')

      return {
        status: 200,
        jsonBody: {
          message: 'Upload successful',
          metadata: uploadedMetadata,
        },
      }
    } catch (error) {
      context.log.error('Upload handler error:', {
        message: error.message,
        stack: error.stack,
        name: error.name,
      })

      return {
        status: 500,
        jsonBody: {
          error: 'Internal server error',
          detail: error.message,
          timestamp: new Date().toISOString(),
        },
      }
    }
  },
})
app.http('lab-report-decrypt', {
  methods: ['GET'],
  authLevel: 'function',
  route: 'lab-report/preview',
  handler: async (req, context) => {
    const docId = req.query.get('docId')
    const metadata = await getLabReportMetadata(docId)

    const sharedKeyCredential = new StorageSharedKeyCredential(
      process.env.AZURE_STORAGE_ACCOUNT_NAME,
      process.env.AZURE_STORAGE_ACCOUNT_KEY,
    )

    const sasToken = generateBlobSASQueryParameters(
      {
        containerName: 'lab-reports',
        blobName: metadata.blobPath,
        permissions: BlobSASPermissions.parse('r'),
        expiresOn: new Date(Date.now() + 5 * 60 * 1000),
      },
      sharedKeyCredential,
    ).toString()

    const downloadUrl = `https://${process.env.AZURE_STORAGE_ACCOUNT_NAME}.blob.core.windows.net/lab-reports/${metadata.blobPath}?${sasToken}`
    const blobRes = await axios.get(downloadUrl, {
      responseType: 'arraybuffer',
    })

    const decryptedBuffer = await decryptBuffer(
      Buffer.from(blobRes.data),
      metadata.encryptionKey,
      metadata.iv,
    )

    const fileType = await fileTypeFromBuffer(decryptedBuffer)

    return {
      status: 200,
      headers: { 'Content-Type': fileType.mime },
      body: decryptedBuffer,
    }
  },
})

// API endpoint to manually trigger OCR processing for existing files
// app.http('lab-report-ocr-process', {
//   methods: ['POST'],
//   authLevel: 'function',
//   route: 'lab-report/ocr/process',
//   handler: async (req, context) => {
//     try {
//       const { fileId, labTestId } = await req.json()

//       if (!fileId || !labTestId) {
//         return {
//           status: 400,
//           jsonBody: { error: 'Missing fileId or labTestId' },
//         }
//       }

//       // Get file metadata
//       const metadata = await getLabReportMetadata(fileId)
//       if (!metadata) {
//         return {
//           status: 404,
//           jsonBody: { error: 'File not found' },
//         }
//       }

//       // Download and decrypt the file
//       const sharedKeyCredential = new StorageSharedKeyCredential(
//         process.env.AZURE_STORAGE_ACCOUNT_NAME,
//         process.env.AZURE_STORAGE_ACCOUNT_KEY,
//       )

//       const sasToken = generateBlobSASQueryParameters(
//         {
//           containerName: 'lab-reports',
//           blobName: metadata.blobPath,
//           permissions: BlobSASPermissions.parse('r'),
//           expiresOn: new Date(Date.now() + 5 * 60 * 1000),
//         },
//         sharedKeyCredential,
//       ).toString()

//       const downloadUrl = `https://${process.env.AZURE_STORAGE_ACCOUNT_NAME}.blob.core.windows.net/lab-reports/${metadata.blobPath}?${sasToken}`
//       const blobRes = await axios.get(downloadUrl, {
//         responseType: 'arraybuffer',
//       })

//       const decryptedBuffer = await decryptBuffer(
//         Buffer.from(blobRes.data),
//         metadata.encryptionKey,
//         metadata.iv,
//       )

//       // Process with OCR
//       try {
//         // Update OCR status to processing
//         await updateLabReportMetadata({
//           ...metadata,
//           ocrStatus: 'processing',
//         })

//         // Send document to OCR service
//         const ocrData = await processDocumentWithOCR(
//           decryptedBuffer,
//           metadata.fileName,
//           context,
//         )

//         // Update metadata with OCR results
//         await updateLabReportMetadata({
//           ...metadata,
//           ocrStatus: 'completed',
//           ocrData: ocrData,
//         })

//         // Update lab tests with OCR results
//         const updateSuccess = await updateLabTestsWithOCRResults(
//           labTestId,
//           ocrData,
//           context,
//         )

//         return {
//           status: 200,
//           jsonBody: {
//             message: 'OCR processing completed successfully',
//             ocrData: ocrData,
//             labTestUpdateSuccess: updateSuccess,
//           },
//         }
//       } catch (ocrError) {
//         context.log.error('OCR processing failed:', ocrError.message)

//         // Update OCR status to failed
//         await updateLabReportMetadata({
//           ...metadata,
//           ocrStatus: 'failed',
//           ocrData: null,
//         })

//         return {
//           status: 500,
//           jsonBody: {
//             error: 'OCR processing failed',
//             detail: ocrError.message,
//           },
//         }
//       }
//     } catch (error) {
//       context.log.error('OCR process handler error:', error.message)
//       return {
//         status: 500,
//         jsonBody: {
//           error: 'Internal server error',
//           detail: error.message,
//         },
//       }
//     }
//   },
// })

// // API endpoint to check OCR status
// app.http('lab-report-ocr-status', {
//   methods: ['GET'],
//   authLevel: 'function',
//   route: 'lab-report/ocr/status',
//   handler: async (req, context) => {
//     try {
//       const fileId = req.query.get('fileId')

//       if (!fileId) {
//         return {
//           status: 400,
//           jsonBody: { error: 'Missing fileId parameter' },
//         }
//       }

//       const metadata = await getLabReportMetadata(fileId)
//       if (!metadata) {
//         return {
//           status: 404,
//           jsonBody: { error: 'File not found' },
//         }
//       }

//       return {
//         status: 200,
//         jsonBody: {
//           fileId: metadata.id,
//           fileName: metadata.fileName,
//           ocrStatus: metadata.ocrStatus,
//           hasOcrData: !!metadata.ocrData,
//           uploadedAt: metadata.uploadedAt,
//           ocrData: metadata.ocrData,
//         },
//       }
//     } catch (error) {
//       context.log.error('OCR status handler error:', error.message)
//       return {
//         status: 500,
//         jsonBody: {
//           error: 'Internal server error',
//           detail: error.message,
//         },
//       }
//     }
//   },
// })
