const userService = require('../services/user-service');
const rolePermissionHandler = require('../handlers/role-permission-handler');
const logging = require('../common/logging');
const { DefaultRoles } = require('../common/roles');

class UserPermissionUtils {
  static async getUserPermissions(userId) {
    try {
      let user = await userService.getUserByB2CUserId(userId);
      if (!user) {
        user = await userService.getUserById(userId);
      }

      if (!user) {
        logging.logInfo(`User not found: ${userId}`);
        return [];
      }

      const userRole = user?.userRole || '';
      const roleId = user?.roleId || '';

      logging.logInfo(
        `Getting user permissions for ${userId}: userRole=${userRole}, roleId=${roleId}`,
      );

      if (userRole === DefaultRoles.SUPER_ADMIN) {
        logging.logInfo(
          `User ${userId} granted all permissions as Super Admin`,
        );
        const { APIPermissions } = require('../common/permissions');
        return APIPermissions.map(permission => permission.key);
      }

      let rolePermissions = null;

      if (roleId) {
        const organizationId = user?.organizationId || '';

        if (organizationId) {
          rolePermissions =
            await rolePermissionHandler.getAPIListbyRoleIdAndOrganization(
              roleId,
              organizationId,
            );
        }
        if (!rolePermissions) {
          rolePermissions = await rolePermissionHandler.getAPIListbyRoleId(roleId);
        }
        if (!rolePermissions) {
          logging.logInfo(`Falling back to roleName lookup for roleName=${userRole}`)
          rolePermissions = await rolePermissionHandler.getAPIListbyRole(userRole);
        }
      } else {
        rolePermissions = await rolePermissionHandler.getAPIListbyRole(userRole);
      }

      const apisArray = Array.isArray(rolePermissions?.APIs) ? rolePermissions.APIs : []
      const apiPermissionKeyValues = Array.from(
        new Set(apisArray.filter(a => a && a.permissionKey).map(a => a.permissionKey)),
      )

      logging.logInfo(
        `Role permissions object for user ${userId}: ${JSON.stringify(
          { permissionKeys: rolePermissions?.permissionKeys || null, apiCount: apisArray.length, apiPermissionKeyCount: apiPermissionKeyValues.length, apiPermissionKeysSample: apiPermissionKeyValues.slice(0,20) },
        )}`,
      )

      const permissionKeys = UserPermissionUtils.extractPermissionKeys(
        rolePermissions,
      )

      logging.logInfo(`Derived permission keys for user ${userId}: ${JSON.stringify(permissionKeys)}`)

      if (!permissionKeys || permissionKeys.length === 0) {
        logging.logInfo(
          `No permissions found for user ${userId} with role ${userRole}`,
        )
        return []
      }

      logging.logInfo(
        `Found ${permissionKeys.length} permissions for user ${userId}`,
      )

      return permissionKeys
    } catch (error) {
      logging.logError(`Error getting permissions for user ${userId}:`, error);
      return [];
    }
  }

  static extractPermissionKeys(rolePermissions) {
    try {
      if (!rolePermissions) return []

      const { APIPermissions } = require('../common/permissions')

      const keys = new Set()
      if (Array.isArray(rolePermissions.permissionKeys)) {
        rolePermissions.permissionKeys.forEach((k) => k && keys.add(k))
      }

      const apis = Array.isArray(rolePermissions.APIs) ? rolePermissions.APIs : []
      apis.forEach((apiPerm) => {
        if (!apiPerm) return
        if (apiPerm.permissionKey) {
          keys.add(apiPerm.permissionKey)
          return
        }

        const storedApi = (apiPerm.api || '').toString().toLowerCase()
        const storedMethods = Array.isArray(apiPerm.methods)
          ? apiPerm.methods.map((m) => (m || '').toString().toUpperCase())
          : apiPerm.methods
          ? [apiPerm.methods.toString().toUpperCase()]
          : []

        const match = APIPermissions.find((p) => {
          if (!p || !Array.isArray(p.apis)) return false
          const apiMatch = p.apis.some((a) => (a || '').toString().toLowerCase() === storedApi)
          if (!apiMatch) return false
          if (!Array.isArray(p.methods) || p.methods.length === 0 || storedMethods.length === 0) return true
          const permMethods = p.methods.map((m) => (m || '').toString().toUpperCase())
          return permMethods.some((m) => storedMethods.includes(m))
        })

        if (match && match.key) keys.add(match.key)
      })

      return Array.from(keys)
    } catch (err) {
      logging.logError('Error extracting permission keys:', err)
      return []
    }
  }

}

module.exports = UserPermissionUtils;
