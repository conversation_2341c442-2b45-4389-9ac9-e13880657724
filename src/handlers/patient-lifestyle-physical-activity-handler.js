const patientLifestylePhysicalActivityService = require('../services/patient-lifestyle-physical-activity-service')
const { jsonResponse } = require('../common/helper')
const { HttpStatusCode } = require('axios')
const logging = require('../common/logging')

class PatientLifestylePhysicalActivityHandler {
  async getCompleteDashboard(req) {
    try {
      const patientId = req.query.get('patientId')
      const dateFilter = req.query.get('dateFilter') || 'last_15_days'
      const customStartDate = req.query.get('customStartDate') || null
      const customEndDate = req.query.get('customEndDate') || null

      if (!patientId) {
        return jsonResponse(
          'Missing required parameter: patientId',
          HttpStatusCode.BadRequest,
        )
      }

      const customDateRange =
        dateFilter === 'custom' && customStartDate && customEndDate
          ? { start: customStartDate, end: customEndDate }
          : null

      const [
        summary,
        activityDistribution,
        intensityDistribution,
        dayWiseData,
        activityRecords,
      ] = await Promise.all([
        patientLifestylePhysicalActivityService.getDashboardSummary(
          patientId,
          dateFilter,
          customDateRange,
        ),
        patientLifestylePhysicalActivityService.getActivityDistribution(
          patientId,
          dateFilter,
          customDateRange,
        ),
        patientLifestylePhysicalActivityService.getIntensityDistribution(
          patientId,
          dateFilter,
          customDateRange,
        ),
        patientLifestylePhysicalActivityService.getDayWiseData(
          patientId,
          dateFilter,
          customDateRange,
        ),
        patientLifestylePhysicalActivityService.getActivityRecords(
          patientId,
          dateFilter,
          customDateRange,
        ),
      ])

      const dashboardData = {
        summary,
        activityDistribution,
        intensityDistribution,
        dayWiseData,
        activityRecords,
        charts: {
          metMinutes: this.formatMetMinutesChart(dayWiseData),
          totalDuration: this.formatTotalDurationChart(dayWiseData),
          intensityStacked: this.formatIntensityStackedChart(dayWiseData),
          activityTypeStacked: this.formatActivityTypeStackedChart(dayWiseData),
        },
        dateFilter,
        customDateRange,
      }

      return jsonResponse(dashboardData)
    } catch (error) {
      logging.logError('Error getting complete dashboard:', error)
      return jsonResponse(
        'Failed to get dashboard data',
        HttpStatusCode.InternalServerError,
      )
    }
  }

  // Format MET Minutes chart data
  formatMetMinutesChart(dayWiseData) {
    return dayWiseData.map((day) => ({
      date: this.formatDateForChart(day.date),
      value: day.totalMetMinutes,
    }))
  }

  // Format Total Duration chart data
  formatTotalDurationChart(dayWiseData) {
    return dayWiseData.map((day) => ({
      date: this.formatDateForChart(day.date),
      value: day.totalDuration,
    }))
  }

  // Format Intensity Stacked chart data
  formatIntensityStackedChart(dayWiseData) {
    return dayWiseData.map((day) => ({
      date: this.formatDateForChart(day.date),
      mild: day.intensityPercentages.Mild || 0,
      moderate: day.intensityPercentages.Moderate || 0,
      intense: day.intensityPercentages.Intense || 0,
    }))
  }

  // Format Activity Type Stacked chart data
  formatActivityTypeStackedChart(dayWiseData) {
    return dayWiseData.map((day) => ({
      date: this.formatDateForChart(day.date),
      aerobics: day.activityTypePercentages.Aerobics || 0,
      strength: day.activityTypePercentages.Strength || 0,
      flexibility: day.activityTypePercentages.Flexibility || 0,
      balance: day.activityTypePercentages.Balance || 0,
    }))
  }

  // Format date for chart display (e.g., "01 May")
  formatDateForChart(dateStr) {
    const date = new Date(dateStr)
    const day = date.getDate().toString().padStart(2, '0')
    const month = date.toLocaleDateString('en-US', { month: 'short' })
    return `${day} ${month}`
  }
}

module.exports = new PatientLifestylePhysicalActivityHandler()
