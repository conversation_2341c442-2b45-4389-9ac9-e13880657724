const { encryptData, decryptData, encryptDataWithRandomIV, decryptDataWithRandomIV } = require('../common/helper');
const _ = require('lodash');

class EncryptionUtils {
  // New methods using random IV for enhanced security
  static encryptSensitiveFieldsSecure(data, sensitiveFields) {
    if (!data || !sensitiveFields || !Array.isArray(sensitiveFields)) {
      return data;
    }

    const result = _.cloneDeep(data);

    sensitiveFields.forEach((field) => {
      if (field.includes("[]")) {
        const [arrayField, subField] = field.split("[]");
        const array = _.get(result, arrayField, []);
        if (Array.isArray(array)) {
          array.forEach((item, index) => {
            const value = _.get(item, subField);
            if (value && typeof value === 'string' && value.trim() !== '') {
              _.set(result, `${arrayField}[${index}].${subField}`, encryptDataWithRandomIV(value));
            }
          });
        }
      } else {
        const value = _.get(result, field);
        if (value && typeof value === 'string' && value.trim() !== '') {
          _.set(result, field, encryptDataWithRandomIV(value));
        }
      }
    });

    return result;
  }

  static decryptSensitiveFieldsSecure(data, sensitiveFields, userPermissions = [], fieldPermissionMapping = {}) {
    if (!data || !sensitiveFields || !Array.isArray(sensitiveFields)) {
      return data;
    }

    const result = _.cloneDeep(data);

    sensitiveFields.forEach((field) => {
      const requiredPermission = fieldPermissionMapping[field];
      const hasPermission = !requiredPermission || 
        userPermissions.includes(requiredPermission) ||
        userPermissions.includes('emr.patientinfo.view.sensitive'); // Master permission

      if (!hasPermission) {
        this.maskSensitiveField(result, field);
        return;
      }

      if (field.includes("[]")) {
        const [arrayField, subField] = field.split("[]");
        const array = _.get(result, arrayField, []);
        if (Array.isArray(array)) {
          array.forEach((item, index) => {
            const value = _.get(item, subField);
            if (value && typeof value === 'string') {
              try {
                _.set(result, `${arrayField}[${index}].${subField}`, decryptDataWithRandomIV(value));
              } catch (error) {
                console.warn(`Failed to decrypt field ${field} at index ${index}:`, error.message);
              }
            }
          });
        }
      } else {
        const value = _.get(result, field);
        if (value && typeof value === 'string') {
          try {
            _.set(result, field, decryptDataWithRandomIV(value));
          } catch (error) {
            console.warn(`Failed to decrypt field ${field}:`, error.message);
          }
        }
      }
    });

    return result;
  }

  // Legacy methods for backward compatibility
  static encryptSensitiveFields(data, sensitiveFields) {
    if (!data || !sensitiveFields || !Array.isArray(sensitiveFields)) {
      return data;
    }

    const result = _.cloneDeep(data);

    sensitiveFields.forEach((field) => {
      if (field.includes("[]")) {
        const [arrayField, subField] = field.split("[]");
        const array = _.get(result, arrayField, []);
        if (Array.isArray(array)) {
          array.forEach((item, index) => {
            const value = _.get(item, subField);
            if (value && typeof value === 'string' && value.trim() !== '') {
              _.set(result, `${arrayField}[${index}].${subField}`, encryptData(value));
            }
          });
        }
      } else {
        const value = _.get(result, field);
        if (value && typeof value === 'string' && value.trim() !== '') {
          _.set(result, field, encryptData(value));
        }
      }
    });

    return result;
  }

  static decryptSensitiveFields(data, sensitiveFields, userPermissions = [], fieldPermissionMapping = {}) {
    if (!data || !sensitiveFields || !Array.isArray(sensitiveFields)) {
      return data;
    }

    const result = _.cloneDeep(data);

    sensitiveFields.forEach((field) => {
      const requiredPermission = fieldPermissionMapping[field];
      const hasPermission = !requiredPermission || 
        userPermissions.includes(requiredPermission) ||
        userPermissions.includes('emr.patientinfo.view.sensitive'); // Master permission

      if (!hasPermission) {
        this.maskSensitiveField(result, field);
        return;
      }

      if (field.includes("[]")) {
        const [arrayField, subField] = field.split("[]");
        const array = _.get(result, arrayField, []);
        if (Array.isArray(array)) {
          array.forEach((item, index) => {
            const value = _.get(item, subField);
            if (value && typeof value === 'string') {
              try {
                _.set(result, `${arrayField}[${index}].${subField}`, decryptData(value));
              } catch (error) {
                console.warn(`Failed to decrypt field ${field} at index ${index}:`, error.message);
              }
            }
          });
        }
      } else {
        const value = _.get(result, field);
        if (value && typeof value === 'string') {
          try {
            _.set(result, field, decryptData(value));
          } catch (error) {
            console.warn(`Failed to decrypt field ${field}:`, error.message);
          }
        }
      }
    });

    return result;
  }

  static maskSensitiveField(data, field) {
    if (field.includes("[]")) {
      const [arrayField, subField] = field.split("[]");
      const array = _.get(data, arrayField, []);
      if (Array.isArray(array)) {
        array.forEach((item, index) => {
          const value = _.get(item, subField);
          if (value) {
            _.set(data, `${arrayField}[${index}].${subField}`, '******');
          }
        });
      }
    } else {
      const value = _.get(data, field);
      if (value) {
        _.set(data, field, '******');
      }
    }
  }
  static getMaskedValue(value) {
    if (!value || typeof value !== 'string') {
      return value;
    }
    
    return '******';
  }

  static shouldEncryptField(fieldPath, encryptedFields) {
    return encryptedFields.includes(fieldPath);
  }
}

module.exports = EncryptionUtils;
