# **Arcaai EMR Microservice \- Complete API Documentation**

## **Base URL**

[*************************************/EMR-MS/api/v0.1](*************************************/EMR-MS/api/v0.1)  

[*************************************/abdm/v0.1](*************************************/abdm/v0.1)

[*************************************/appointment/v0.1](*************************************/appointment/v0.1)

[*************************************/consultation/v0.1](*************************************/consultation/v0.1)

[*************************************/dashboard/v0.1](*************************************/dashboard/v0.1)

[*************************************/doctor/v0.1](*************************************/doctor/v0.1)

[*************************************/lab-test/v0.1](*************************************/lab-test/v0.1)

[*************************************/lifestyle/v0.1](*************************************/lifestyle/v0.1)

[*************************************/consultation/v0.1](*************************************/consultation/v0.1)

[*************************************/dashboard/v0.1](*************************************/dashboard/v0.1)

[*************************************/medicine/v0.1](*************************************/medicine/v0.1)

[*************************************/nutrition/v0.1](*************************************/nutrition/v0.1)

[*************************************/organization/v0.1](*************************************/organization/v0.1)

[*************************************/patient/v0.1](*************************************/patient/v0.1)

[*************************************/patient-hystory/v0.1](*************************************/patient-hystory/v0.1)

[*************************************/payment/v0.1](*************************************/payment/v0.1)

[*************************************/permission/v0.1](*************************************/permission/v0.1)

[*************************************/prescription-package/v0.1](*************************************/prescription-package/v0.1)

[*************************************/test-package/v0.1](*************************************/test-package/v0.1)

[*************************************/user/v0.1](*************************************/user/v0.1)

[*************************************/diognosis/v0.1](*************************************/diognosis/v0.1)

[*************************************/role/v0.1](*************************************/role/v0.1)

## **Use [*************************************/EMR-MS/api/v0.1](*************************************/EMR-MS/api/v0.1) as the base URL for all APIs unless specified otherwise**

## **Authentication**

All APIs require JWT Bearer token authentication unless specified otherwise.

Authorization: Bearer \<jwt\_token\>

**Ocp-Apim-Subscription-Key : ac5d8ae93c0f4f9892390f9c9646e749**

## **Common Response Format**

{  
  "data": {},  
  "message": "Success",  
  "status": 200  
}

## **Error Response Format**

{  
  "error": "Error message",  
  "status": 400**/**401**/**403**/**404**/**500  
}

---

## **1\. User Management APIs**

Base url: [*************************************/user/v0.1](*************************************/user/v0.1)

### **Get User Details**

**GET** /user?email={email} **GET** /user?userType={userType}

**Response:**

{  
  "id": "user-id",  
  "email": "<EMAIL>",  
  "name": "User Name",  
  "userRole": "doctor",  
  "userType": "doctor",  
  "organizationId": "org-id",  
  "isActive": **true**,  
  "created\_on": "2024-01-01T00:00:00.000Z"  
}

### **Create User**

**POST** /user

**Request Body:**

{  
  "name": "User Name",  
  "email": "<EMAIL>",  
  "userRole": "doctor",  
  "userType": "doctor",  
  "organizationId": "org-id",  
  "isActive": **true**  
}

**Response:**

{  
  "id": "user-id",  
  "message": "User created successfully",  
  "status": 201  
}

### **Update User**

**PATCH** /user

**Request Body:**

{  
  "id": "user-id",  
  "name": "Updated Name",  
  "isActive": **false**  
}

**Response:**

{  
  "message": "User updated successfully",  
  "status": 200  
}

### **Delete User**

**DELETE** /user?userId={userId}

**Response:**

{  
  "message": "User deleted successfully",  
  "status": 200  
}

### **Get Users by Organization**

**GET** /user/list?organizationId={organizationId}\&page={page}\&pageSize={pageSize}

**Response:**

{  
  "users": \[  
    {  
      "id": "user-id",  
      "name": "User Name",  
      "email": "<EMAIL>",  
      "userRole": "doctor",  
      "isActive": **true**  
    }  
  \],  
  "totalCount": 50,  
  "currentPage": 1,  
  "totalPages": 5  
}

### **User Signup (External Integration)**

**POST** /usersignup *Requires Basic Authentication*

**Request Body:**

{  
  "name": "User Name",  
  "email": "<EMAIL>",  
  "userType": "doctor",  
  "organizationId": "org-id"  
}

**Response:**

{  
  "id": "user-id",  
  "message": "User registered successfully",  
  "status": 201  
}

### **Upload User Document**

**POST** /user/document/upload *Content-Type: multipart/form-data*

**Request Body:**

file: \[document.pdf\]  
doc\_type: "profile\_picture"  
userId: "user-id"

**Response:**

{  
  "blobUrl": "https://storage.blob.core.windows.net/file-uploads/user/user-id/profile\_picture/document.pdf"  
}

---

## **2\. Organization APIs**

Base url:[*************************************/organization/v0.1](*************************************/organization/v0.1)

### **List Organizations**

**GET** /list-organizations?searchText={searchText}\&page={page}\&pageSize={pageSize}

**Response:**

{  
  "organizations": \[  
    {  
      "id": "org-id",  
      "name": "Hospital Name",  
      "email": "<EMAIL>",  
      "contactPerson": "Admin Name",  
      "phone": "+**********",  
      "address": "Hospital Address",  
      "isActive": **true**  
    }  
  \],  
  "totalCount": 100,  
  "currentPage": 1,  
  "totalPages": 10  
}

### **Get Organization Patients**

**GET** /organization/patients?organizationId={organizationId}\&page={page}\&pageSize={pageSize}

**Response:**

{  
  "patients": \[  
    {  
      "id": "patient-id",  
      "name": "Patient Name",  
      "email": "<EMAIL>",  
      "phone": "+**********",  
      "dateOfBirth": "1990-01-01"  
    }  
  \],  
  "totalCount": 500,  
  "currentPage": 1,  
  "totalPages": 50  
}

### **Get Organization Medicines**

Base url: [*************************************/medicine/v0.1](*************************************/medicine/v0.1)

**GET** /organization/medicines?organizationId={organizationId}\&page={page}\&pageSize={pageSize}

**Response:**

{  
  "medicines": \[  
    {  
      "medicineId": "med-id",  
      "name": "Medicine Name",  
      "isActive": **true**,  
      "price": 100.50,  
      "organizationId": "org-id"  
    }  
  \],  
  "totalCount": 200,  
  "currentPage": 1,  
  "totalPages": 20  
}

### **Update Organization Medicines**

**Base url: [*************************************/medicine/v0.1](*************************************/medicine/v0.1)**

**POST** /organization/medicines/update

**Request Body:**

{  
  "organizationId": "org-id",  
  "medicines": \[  
    {  
      "medicineId": "med-id",  
      "isActive": **true**,  
      "price": 100.50  
    }  
  \]  
}

**Response:**

{  
  "message": "Organization medicines updated successfully",  
  "updatedCount": 1,  
  "status": 200  
}

### **Get Organization Payments**

**Base url:  [*************************************/payment/v0.1](*************************************/payment/v0.1)**

**GET** /payments/organization?organizationId={organizationId}\&page={page}\&pageSize={pageSize}

**Response:**

{  
  "payments": \[  
    {  
      "id": "payment-id",  
      "amount": 500,  
      "currency": "INR",  
      "status": "completed",  
      "paymentType": "consultation",  
      "patientId": "patient-id",  
      "created\_on": "2024-01-01T00:00:00.000Z"  
    }  
  \],  
  "totalCount": 100,  
  "currentPage": 1,  
  "totalPages": 10  
}

---

## **3\. Patient APIs**

### **Get Patient Profile**

**GET** /patient?id={patientId}

**Response:**

{  
  "id": "**********",  
  "personalDetails": {  
    "firstName": "John",  
    "lastName": "Doe",  
    "dateOfBirth": "1990-01-01",  
    "gender": "male",  
    "contactNumber": "+**********",  
    "email": "<EMAIL>",  
    "address": "Patient Address"  
  },  
  "medicalHistory": \[\],  
  "emergencyContacts": \[\],  
  "insurance": \[\],  
  "created\_on": "2024-01-01T00:00:00.000Z"  
}

### **Create Patient**

**POST** /patient

**Request Body:**

{  
  "personalDetails": {  
    "firstName": "John",  
    "lastName": "Doe",  
    "dateOfBirth": "1990-01-01",  
    "gender": "male",  
    "contactNumber": "+**********",  
    "email": "<EMAIL>"  
  },  
  "address": "Patient Address",  
  "emergencyContacts": \[\]  
}

**Response:**

{  
  "id": "**********",  
  "message": "Patient created successfully",  
  "status": 201  
}

### **Update Patient**

**PUT** /patient **PATCH** /patient

**Request Body:**

{  
  "id": "patient-id",  
  "personalDetails": {  
    "firstName": "Updated Name"  
  }  
}

**Response:**

{  
  "message": "Patient updated successfully",  
  "status": 200  
}

### **Search Patients**

**POST** /patient/search

**Request Body:**

{  
  "query": "John Doe",  
  "pagesize": 10,  
  "continuetoken": "continuation-token"  
}

**Response:**

{  
  "patients": \[  
    {  
      "id": "patient-id",  
      "name": "John Doe",  
      "email": "<EMAIL>",  
      "phone": "+**********",  
      "dateOfBirth": "1990-01-01"  
    }  
  \],  
  "continuationToken": "next-token",  
  "hasMoreResults": **true**,  
  "totalFetched": 10  
}

---

## **4\. Patient Medical Records APIs**

### **Patient History**

**GET** /patient/history?patientId={patientId}\&startDate={startDate}\&endDate={endDate}

**Response:**

{  
  "patientId": "patient-id",  
  "history": \[  
    {  
      "id": "history-id",  
      "date": "2024-01-01",  
      "diagnosis": "Hypertension",  
      "treatment": "Medication prescribed",  
      "doctorId": "doctor-id",  
      "notes": "Patient responding well to treatment"  
    }  
  \]  
}

**POST** /patient/history?patientId={patientId}

**Request Body:**

{  
  "diagnosis": "Diabetes Type 2",  
  "treatment": "Insulin therapy",  
  "notes": "Patient education provided",  
  "symptoms": \["increased thirst", "frequent urination"\],  
  "medications": \[  
    {  
      "name": "Metformin",  
      "dosage": "500mg",  
      "frequency": "twice daily"  
    }  
  \]  
}

**Response:**

{  
  "id": "history-id",  
  "message": "Patient history created successfully",  
  "status": 201  
}

**PUT** /patient/history?patientId={patientId}

**Request Body:**

{  
  "id": "history-id",  
  "diagnosis": "Updated diagnosis",  
  "treatment": "Updated treatment plan",  
  "notes": "Updated notes"  
}

**Response:**

{  
  "message": "Patient history updated successfully",  
  "status": 200  
}

### **Patient Vitals**

**GET** /patient/vitals?patientId={patientId}

**Response:**

{  
  "patientId": "patient-id",  
  "vitals": \[  
    {  
      "id": "vital-id",  
      "date": "2024-01-01T10:00:00.000Z",  
      "bloodPressure": {  
        "systolic": 120,  
        "diastolic": 80,  
        "status": "normal"  
      },  
      "heartRate": {  
        "value": 72,  
        "status": "normal"  
      },  
      "temperature": {  
        "value": 98.6,  
        "unit": "F",  
        "status": "normal"  
      },  
      "weight": {  
        "value": 70,  
        "unit": "kg"  
      },  
      "height": {  
        "value": 175,  
        "unit": "cm"  
      }  
    }  
  \]  
}

**POST** /patient/vitals?patientId={patientId}

**Request Body:**

{  
  "bloodPressure": {  
    "systolic": 130,  
    "diastolic": 85  
  },  
  "heartRate": 75,  
  "temperature": 99.1,  
  "weight": 72,  
  "height": 175,  
  "recordedBy": "nurse-id",  
  "notes": "Patient appears stable"  
}

**Response:**

{  
  "id": "vital-id",  
  "message": "Patient vitals recorded successfully",  
  "status": 201  
}

**PUT** /patient/vitals?patientId={patientId}

**Request Body:**

{  
  "id": "vital-id",  
  "bloodPressure": {  
    "systolic": 125,  
    "diastolic": 82  
  },  
  "notes": "Updated reading"  
}

**Response:**

{  
  "message": "Patient vitals updated successfully",  
  "status": 200  
}

### **Patient Consultation**

**GET** /patient-consultation?patientId={patientId}

**Response:**

{  
  "patientId": "patient-id",  
  "consultations": \[  
    {  
      "id": "consultation-id",  
      "doctorId": "doctor-id",  
      "consultationDate": "2024-01-01",  
      "chiefComplaint": "Chest pain",  
      "diagnosis": "Angina",  
      "treatment": "Medication and lifestyle changes",  
      "notes": "Follow up in 2 weeks",  
      "status": "completed"  
    }  
  \]  
}

**POST** /patient-consultation?patientId={patientId}

**Request Body:**

{  
  "doctorId": "doctor-id",  
  "consultationDate": "2024-01-01",  
  "chiefComplaint": "Headache and fever",  
  "presentIllness": "Patient reports headache for 2 days",  
  "examination": "Temperature 101F, BP 120/80",  
  "diagnosis": "Viral fever",  
  "treatment": "Paracetamol 500mg TID",  
  "notes": "Rest and hydration advised"  
}

**Response:**

{  
  "id": "consultation-id",  
  "message": "Consultation created successfully",  
  "status": 201  
}

**PUT** /patient-consultation?patientId={patientId}

**Request Body:**

{  
  "id": "consultation-id",  
  "diagnosis": "Updated diagnosis",  
  "treatment": "Updated treatment plan",  
  "notes": "Updated consultation notes"  
}

**Response:**

{  
  "message": "Consultation updated successfully",  
  "status": 200  
}

### **Patient Consulting**

**GET** /patient/consulting?patientId={patientId}\&date={date}

**Response:**

{  
  "patientId": "patient-id",  
  "consultingRecords": \[  
    {  
      "id": "consulting-id",  
      "date": "2024-01-01",  
      "doctorId": "doctor-id",  
      "consultationType": "follow-up",  
      "duration": 30,  
      "status": "completed"  
    }  
  \]  
}

**POST** /patient/consulting?patientId={patientId}

**Request Body:**

{  
  "doctorId": "doctor-id",  
  "consultationType": "initial",  
  "scheduledDate": "2024-01-01",  
  "duration": 45,  
  "notes": "Initial consultation for new patient"  
}

**Response:**

{  
  "id": "consulting-id",  
  "message": "Consulting record created successfully",  
  "status": 201  
}

**PUT** /patient/consulting?patientId={patientId}

**Request Body:**

{  
  "id": "consulting-id",  
  "status": "completed",  
  "actualDuration": 40,  
  "notes": "Consultation completed successfully"  
}

**Response:**

{  
  "message": "Consulting record updated successfully",  
  "status": 200  
}

### **Patient Diagnosis Notes**

**GET** /patient/diagnosis-notes?patientId={patientId}

**Response:**

{  
  "patientId": "patient-id",  
  "diagnosisNotes": \[  
    {  
      "id": "note-id",  
      "date": "2024-01-01",  
      "doctorId": "doctor-id",  
      "diagnosis": "Hypertension",  
      "icdCode": "I10",  
      "severity": "mild",  
      "notes": "Patient has mild hypertension, lifestyle modifications recommended",  
      "followUpRequired": **true**,  
      "followUpDate": "2024-02-01"  
    }  
  \]  
}

---

## **5\. Patient Lifestyle APIs**

### **Patient Lifestyle**

**GET** /patient/lifestyle?patientId={patientId}

**Response:**

{  
  "patientId": "patient-id",  
  "lifestyle": {  
    "id": "lifestyle-id",  
    "physicalActivity": {  
      "exerciseFrequency": "3 times per week",  
      "exerciseType": "cardio",  
      "duration": 30,  
      "intensity": "moderate"  
    },  
    "nutrition": {  
      "dietType": "balanced",  
      "mealsPerDay": 3,  
      "waterIntake": "2 liters",  
      "restrictions": \["gluten-free"\]  
    },  
    "sleepPattern": {  
      "averageHours": 7,  
      "bedtime": "22:00",  
      "wakeupTime": "06:00",  
      "quality": "good"  
    },  
    "habits": {  
      "smoking": "no",  
      "alcohol": "occasional",  
      "caffeine": "moderate"  
    },  
    "created\_on": "2024-01-01T00:00:00.000Z",  
    "updated\_on": "2024-01-01T00:00:00.000Z"  
  }  
}

**POST** /patient/lifestyle?patientId={patientId}

**Request Body:**

{  
  "physicalActivity": {  
    "exerciseFrequency": "daily",  
    "exerciseType": "mixed",  
    "duration": 45,  
    "intensity": "high"  
  },  
  "nutrition": {  
    "dietType": "vegetarian",  
    "mealsPerDay": 4,  
    "waterIntake": "3 liters",  
    "restrictions": \["dairy-free"\]  
  },  
  "sleepPattern": {  
    "averageHours": 8,  
    "bedtime": "21:30",  
    "wakeupTime": "05:30",  
    "quality": "excellent"  
  },  
  "habits": {  
    "smoking": "former",  
    "alcohol": "no",  
    "caffeine": "low"  
  }  
}

**Response:**

{  
  "id": "lifestyle-id",  
  "message": "Patient lifestyle created successfully",  
  "status": 201  
}

**PATCH** /patient/lifestyle?patientId={patientId}

**Request Body:**

{  
  "id": "lifestyle-id",  
  "physicalActivity": {  
    "exerciseFrequency": "5 times per week",  
    "duration": 60  
  },  
  "habits": {  
    "smoking": "no"  
  }  
}

**Response:**

{  
  "message": "Patient lifestyle updated successfully",  
  "status": 200  
}

**DELETE** /patient/lifestyle?patientId={patientId}

**Response:**

{  
  "message": "Patient lifestyle deleted successfully",  
  "status": 200  
}

### **Lifestyle Questions**

**GET** /lifestyle/question?source={source}\&section={section}

**Response:**

{  
  "source": "physical\_activity\_attitude",  
  "questions": \[  
    {  
      "id": "attitude",  
      "title": "Attitude",  
      "icon": "attitude",  
      "fields": \[  
        {  
          "id": "exercise\_preference",  
          "label": "Do you like doing exercise?",  
          "type": "radio",  
          "options": \["Yes", "No"\],  
          "required": **true**  
        },  
        {  
          "id": "reason\_for\_choice",  
          "label": "Reason for your choice",  
          "type": "section",  
          "fields": \[  
            {  
              "id": "knowledge\_of\_exercise",  
              "label": "Knowledge of exercise",  
              "type": "radio",  
              "options": \[  
                "It is good for my health",  
                "I am not sure about the health benefits"  
              \]  
            }  
          \]  
        }  
      \]  
    }  
  \]  
}

**POST** /lifestyle/question?source={source}

**Request Body:**

{  
  "source": "nutrition\_knowledge",  
  "questions": \[  
    {  
      "id": "nutrition\_basics",  
      "title": "Nutrition Basics",  
      "icon": "nutrition",  
      "fields": \[  
        {  
          "id": "daily\_meals",  
          "label": "How many meals do you eat per day?",  
          "type": "number",  
          "min": 1,  
          "max": 6,  
          "required": **true**  
        },  
        {  
          "id": "water\_intake",  
          "label": "Daily water intake",  
          "type": "select",  
          "options": \["Less than 1L", "1-2L", "2-3L", "More than 3L"\]  
        }  
      \]  
    }  
  \]  
}

**Response:**

{  
  "id": "question-id",  
  "message": "Lifestyle questions created successfully",  
  "status": 201  
}

**PATCH** /lifestyle/question?id={id}

**Request Body:**

{  
  "questions": \[  
    {  
      "id": "nutrition\_basics",  
      "title": "Updated Nutrition Basics",  
      "fields": \[  
        {  
          "id": "daily\_meals",  
          "label": "How many meals do you typically eat per day?",  
          "type": "number",  
          "min": 1,  
          "max": 8  
        }  
      \]  
    }  
  \]  
}

**Response:**

{  
  "message": "Lifestyle questions updated successfully",  
  "status": 200  
}

**DELETE** /lifestyle/question?id={id}

**Response:**

{  
  "message": "Lifestyle questions deleted successfully",  
  "status": 200  
}

### **Lifestyle Ambient Listening**

**Base url: [*************************************/lifestyle/v0.1](*************************************/lifestyle/v0.1)**

**POST** /lifestyle/ambient-listening

**Request Body:**

{

 "source": "physical\_activity\_practice\_exercise\_patterns",

 "transcript": "Hello, I’d like to ask you some questions about your physical activity habits. Sure, please go ahead. Can you tell me what types of exercise activities you engage in? I usually do strength and flexibility exercises. Let’s start with strength activities. What type specifically? I do free weight exercises. How often do you do this activity? Three times a week. And what is the intensity level for this? I’d say mild. Noted. Now coming to flexibility exercises—what do you usually do? I mostly do static stretching. What’s the intensity for this one? Moderate. Do you have a specific frequency or duration for flexibility exercises? I haven’t fixed a frequency or duration for that yet. Alright. Do you do any other types of physical activities like aerobics or balance-focused workouts? No, not regularly—just the strength and flexibility ones for now. Thanks, that helps understand your current physical activity pattern."

}

**Response:**

	{

   "conversation": \[

       {

           "speaker": "doctor",

           "message": "Hello, I’d like to ask you some questions about your physical activity habits."

       },

       {

           "speaker": "patient",

           "message": "Sure, please go ahead."

       },

       {

           "speaker": "doctor",

           "message": "Can you tell me what types of exercise activities you engage in?"

       },

       {

           "speaker": "patient",

           "message": "I usually do strength and flexibility exercises."

       },

       {

           "speaker": "doctor",

           "message": "Let’s start with strength activities. What type specifically?"

       },

       {

           "speaker": "patient",

           "message": "I do free weight exercises."

       },

       {

           "speaker": "doctor",

           "message": "How often do you do this activity?"

       },

       {

           "speaker": "patient",

           "message": "Three times a week."

       },

       {

           "speaker": "doctor",

           "message": "And what is the intensity level for this?"

       },

       {

           "speaker": "patient",

           "message": "I’d say mild."

       },

       {

           "speaker": "doctor",

           "message": "Noted. Now coming to flexibility exercises—what do you usually do?"

       },

       {

           "speaker": "patient",

           "message": "I mostly do static stretching."

       },

       {

           "speaker": "doctor",

           "message": "What’s the intensity for this one?"

       },

       {

           "speaker": "patient",

           "message": "Moderate."

       },

       {

           "speaker": "doctor",

           "message": "Do you have a specific frequency or duration for flexibility exercises?"

       },

       {

           "speaker": "patient",

           "message": "I haven’t fixed a frequency or duration for that yet."

       },

       {

           "speaker": "doctor",

           "message": "Alright. Do you do any other types of physical activities like aerobics or balance-focused workouts?"

       },

       {

           "speaker": "patient",

           "message": "No, not regularly—just the strength and flexibility ones for now."

       },

       {

           "speaker": "doctor",

           "message": "Thanks, that helps understand your current physical activity pattern."

       }

   \],

   "summary": {

       "questions": \[

           {

               "id": "exercise\_patterns",

               "title": "Exercise Patterns",

               "icon": "dumbbell",

               "fields": \[

                   {

                       "id": "exercise\_table",

                       "label": "Exercise Activities",

                       "type": "table",

                       "headers": \[

                           {

                               "id": "activity\_type",

                               "label": "Activity Type",

                               "type": "select",

                               "options": \[

                                   "Aerobics",

                                   "Strength",

                                   "Flexibility",

                                   "Balance"

                               \]

                           },

                           {

                               "id": "activity",

                               "label": "Activity",

                               "type": "conditional\_select",

                               "dependsOn": "activity\_type",

                               "options": {

                                   "Aerobics": \[

                                       "Walking",

                                       "Jogging",

                                       "Running",

                                       "Cycling",

                                       "Swimming",

                                       "Dancing",

                                       "Zumba",

                                       "Skipping",

                                       "Kickboxing",

                                       "Rowing",

                                       "Hiking",

                                       "Stair Climbing",

                                       "Sports-Cricket,Basketball,Football,Badminton,Lawn Tennis,Golf"

                                   \],

                                   "Strength": \[

                                       "Body Weight Exercises",

                                       "Free Weight Exercises",

                                       "Machine Based Exercise",

                                       "Resistance Band Exercise",

                                       "Functional Strenght Exercise"

                                   \],

                                   "Flexibility": \[

                                       "Static Stretching",

                                       "Dynamic Stretching",

                                       "Yoga",

                                       "Pilates based flexibility",

                                       "Foam Rolling"

                                   \],

                                   "Balance": \[

                                       "Basic Balance Exercise",

                                       "Balance Enhancing Yoga Poses",

                                       "Advanced Functional Balance Exercise",

                                       "Sports Specific or dynamic Balance drills",

                                       "Foam Rolling"

                                   \]

                               }

                           },

                           {

                               "id": "duration",

                               "label": "Duration(min)",

                               "type": "number"

                           },

                           {

                               "id": "intensity",

                               "label": "Intensity",

                               "type": "select",

                               "options": \[

                                   "Mild",

                                   "Moderate",

                                   "Intense"

                               \]

                           },

                           {

                               "id": "frequency",

                               "label": "Frequency",

                               "type": "select",

                               "options": \[

                                   "Daily",

                                   "Three times a week",

                                   "Four times a week",

                                   "Five times a week",

                                   "Six times a week"

                               \]

                           }

                       \],

                       "value": \[

                           {

                               "activity\_type": "Strength",

                               "activity": "Free weight exercises",

                               "duration": "",

                               "intensity": "Mild",

                               "frequency": "Three times a week"

                           },

                           {

                               "activity\_type": "Flexibility",

                               "activity": "Static stretching",

                               "duration": "",

                               "intensity": "Moderate",

                               "frequency": ""

                           }

                       \]

                   }

               \]

           }

       \]

   }

}

### **Patient Lifestyle Note**

**GET** /patient/lifestyle/note?patientId={patientId}

**Response:**

{  
  "patientId": "patient-id",  
  "lifestyleNotes": \[  
    {  
      "id": "note-id",  
      "date": "2024-01-01",  
      "category": "physical\_activity",  
      "note": "Patient has increased exercise frequency from 2 to 3 times per week",  
      "createdBy": "doctor-id",  
      "priority": "medium"  
    }  
  \]  
}

**POST** /patient/lifestyle/note?patientId={patientId}

**Request Body:**

{  
  "category": "nutrition",  
  "note": "Patient has adopted a plant-based diet and reports feeling more energetic",  
  "priority": "high",  
  "tags": \["diet-change", "energy-improvement"\]  
}

**Response:**

{  
  "id": "note-id",  
  "message": "Lifestyle note created successfully",  
  "status": 201  
}

**PATCH** /patient/lifestyle/note?patientId={patientId}

**Request Body:**

{  
  "id": "note-id",  
  "note": "Updated note: Patient has maintained plant-based diet for 3 months with excellent results",  
  "priority": "high"  
}

**Response:**

{  
  "message": "Lifestyle note updated successfully",  
  "status": 200  
}

### **Patient Demographics**

**Base url: *************************************/lifestyle/v0.1**

**GET** /patient/lifestyle/demographics?patientId={patientId}

**Response:**

{  
  "patientId": "patient-id",  
  "demographics": {  
    "id": "demographics-id",  
    "personalInfo": {  
      "age": 35,  
      "gender": "male",  
      "maritalStatus": "married",  
      "occupation": "software engineer",  
      "education": "bachelor's degree",  
      "income": "middle"  
    },  
    "familyInfo": {  
      "householdSize": 4,  
      "children": 2,  
      "dependents": 3  
    },  
    "locationInfo": {  
      "city": "Mumbai",  
      "state": "Maharashtra",  
      "country": "India",  
      "zipCode": "400001",  
      "residenceType": "apartment"  
    },  
    "socioeconomicFactors": {  
      "employmentStatus": "employed",  
      "healthInsurance": **true**,  
      "transportationAccess": "private vehicle"  
    }  
  }  
}

**POST** /patient/lifestyle/demographics?patientId={patientId}

**Request Body:**

{  
  "personalInfo": {  
    "age": 28,  
    "gender": "female",  
    "maritalStatus": "single",  
    "occupation": "teacher",  
    "education": "master's degree",  
    "income": "middle"  
  },  
  "familyInfo": {  
    "householdSize": 2,  
    "children": 0,  
    "dependents": 1  
  },  
  "locationInfo": {  
    "city": "Delhi",  
    "state": "Delhi",  
    "country": "India",  
    "zipCode": "110001",  
    "residenceType": "house"  
  },  
  "socioeconomicFactors": {  
    "employmentStatus": "employed",  
    "healthInsurance": **true**,  
    "transportationAccess": "public transport"  
  }  
}

**Response:**

{  
  "id": "demographics-id",  
  "message": "Patient demographics created successfully",  
  "status": 201  
}

**PUT** /patient/lifestyle/demographics?patientId={patientId}

**Request Body:**

{  
  "id": "demographics-id",  
  "personalInfo": {  
    "age": 29,  
    "maritalStatus": "married",  
    "occupation": "senior teacher"  
  },  
  "familyInfo": {  
    "householdSize": 3,  
    "children": 1  
  }  
}

**Response:**

{  
  "message": "Patient demographics updated successfully",  
  "status": 200  
}

### 

### 

### **Medical History Addiction**

**Base url: [*************************************/lifestyle/v0.1](*************************************/lifestyle/v0.1)**

**GET** /patient/lifestyle/medical-history-addiction?patientId={patientId}\&id={id}

**Response:**

{  
  "patientId": "patient-id",  
  "medicalHistoryAddiction": {  
    "id": "addiction-id",  
    "diagnosis": \[  
      {  
        "condition": "Nicotine dependence",  
        "icdCode": "F17.2",  
        "diagnosisDate": "2023-06-15",  
        "status": "active"  
      }  
    \],  
    "substanceUse": {  
      "smoking": {  
        "status": "current",  
        "frequency": "daily",  
        "quantity": "10 cigarettes per day",  
        "startDate": "2010-01-01",  
        "quitAttempts": 3  
      },  
      "alcohol": {  
        "status": "former",  
        "frequency": "occasionally",  
        "lastUse": "2022-12-31",  
        "quitDate": "2023-01-01"  
      },  
      "tobacco": {  
        "status": "no",  
        "frequency": **null**,  
        "lastUse": **null**  
      },  
      "drugs": {  
        "status": "no",  
        "frequency": **null**,  
        "substances": \[\]  
      }  
    },  
    "assessments": {  
      "nicotineDependenceTest": {  
        "score": 7,  
        "severity": "high",  
        "date": "2023-06-15"  
      }  
    },  
    "treatmentHistory": \[  
      {  
        "type": "nicotine replacement therapy",  
        "startDate": "2023-07-01",  
        "endDate": "2023-09-01",  
        "outcome": "partial success"  
      }  
    \]  
  }  
}

**POST** /patient/lifestyle/medical-history-addiction?patientId={patientId}

**Request Body:**

{  
  "diagnosis": \[  
    {  
      "condition": "Alcohol use disorder",  
      "icdCode": "F10.2",  
      "diagnosisDate": "2024-01-15",  
      "status": "active",  
      "severity": "moderate"  
    }  
  \],  
  "substanceUse": {  
    "smoking": {  
      "status": "no",  
      "frequency": **null**  
    },  
    "alcohol": {  
      "status": "current",  
      "frequency": "daily",  
      "quantity": "3-4 drinks per day",  
      "startDate": "2015-01-01"  
    },  
    "tobacco": {  
      "status": "former",  
      "frequency": "daily",  
      "quitDate": "2020-01-01"  
    },  
    "drugs": {  
      "status": "no",  
      "frequency": **null**,  
      "substances": \[\]  
    }  
  },  
  "assessments": {  
    "auditScore": {  
      "score": 15,  
      "severity": "moderate",  
      "date": "2024-01-15"  
    }  
  }  
}

**Response:**

{  
  "id": "addiction-id",  
  "message": "Medical history addiction record created successfully",  
  "status": 201  
}

**PUT** /patient/lifestyle/medical-history-addiction?patientId={patientId}

**Request Body:**

{  
  "id": "addiction-id",  
  "substanceUse": {  
    "alcohol": {  
      "status": "former",  
      "frequency": **null**,  
      "quitDate": "2024-02-01"  
    }  
  },  
  "treatmentHistory": \[  
    {  
      "type": "counseling",  
      "startDate": "2024-01-20",  
      "endDate": "2024-03-20",  
      "outcome": "successful"  
    }  
  \]  
}

**Response:**

{  
  "message": "Medical history addiction record updated successfully",  
  "status": 200  
}

**DELETE** /patient/lifestyle/medical-history-addiction?id={id}

**Response:**

{  
  "message": "Medical history addiction record deleted successfully",  
  "status": 200  
}

### **Physical Activity Dashboard APIs**

**Base URL: *************************************/lifestyle/v0.1**

#### **Complete Physical Activity Dashboard**

**GET** /physical-activity/dashboard?patientId={patientId}&dateFilter={dateFilter}&customStartDate={customStartDate}&customEndDate={customEndDate}

**Query Parameters:**
- `patientId` (required): Patient ID
- `dateFilter` (optional): Date filter - `today`, `yesterday`, `last_7_days`, `last_15_days`, `last_30_days`, `last_90_days`, `custom`, `all`. Default: `last_15_days`
- `customStartDate` (optional): Start date for custom range (YYYY-MM-DD format)
- `customEndDate` (optional): End date for custom range (YYYY-MM-DD format)

**Response:**

```json
{
  "summary": {
    "totalCaloriesSpent": 25104,
    "totalSessions": 10,
    "totalDuration": 3801,
    "avgCaloriesSpent": 2510,
    "avgIntensity": "Mild",
    "avgDuration": 380
  },
  "activityDistribution": [
    {
      "activityType": "Aerobics",
      "duration": 1200,
      "percentage": 32,
      "count": 4
    },
    {
      "activityType": "Strength",
      "duration": 900,
      "percentage": 24,
      "count": 3
    }
  ],
  "intensityDistribution": [
    {
      "intensity": "Mild",
      "duration": 1800,
      "percentage": 47,
      "count": 6
    },
    {
      "intensity": "Moderate",
      "duration": 1200,
      "percentage": 32,
      "count": 3
    }
  ],
  "dayWiseData": [
    {
      "date": "2025-07-20",
      "totalDuration": 60,
      "totalMetMinutes": 180,
      "totalCalories": 420,
      "activityTypePercentages": {
        "Aerobics": 100
      },
      "intensityPercentages": {
        "Mild": 100
      }
    }
  ],
  "activityRecords": [
    {
      "date": "2025-07-20",
      "activityType": "Aerobics",
      "activity": "Walking",
      "intensity": "Mild",
      "duration": 60,
      "caloriesBurned": 420
    }
  ],
  "charts": {
    "metMinutes": [
      {
        "date": "20 Jul",
        "value": 180
      }
    ],
    "totalDuration": [
      {
        "date": "20 Jul",
        "value": 60
      }
    ],
    "intensityStacked": [
      {
        "date": "20 Jul",
        "mild": 100,
        "moderate": 0,
        "intense": 0
      }
    ],
    "activityTypeStacked": [
      {
        "date": "20 Jul",
        "aerobics": 100,
        "strength": 0,
        "flexibility": 0,
        "balance": 0
      }
    ]
  },
  "dateFilter": "last_15_days",
  "customDateRange": null
}
```

#### **Available Physical Activities**

**GET** /physical-activity/list?activityType={activityType}&search={search}

**Query Parameters:**
- `activityType` (optional): Filter by activity type (Aerobics, Strength, Flexibility, Balance)
- `search` (optional): Search term for activity names

**Response:**

```json
{
  "activities": [
    {
      "activityType": "Aerobics",
      "activity": "Walking",
      "metValues": {
        "mild": 2.0,
        "moderate": 3.8,
        "intense": 6.5
      }
    },
    {
      "activityType": "Strength",
      "activity": "Weight Lifting",
      "metValues": {
        "mild": 3.0,
        "moderate": 5.0,
        "intense": 6.0
      }
    }
  ],
  "activityTypes": [
    "Aerobics",
    "Balance",
    "Flexibility",
    "Strength"
  ]
}
```

**Date Filter Options:**
- `today`: Current day only
- `yesterday`: Previous day only
- `last_7_days`: Last 7 days including today
- `last_15_days`: Last 15 days including today (default)
- `last_30_days`: Last 30 days including today
- `last_90_days`: Last 90 days including today
- `custom`: Custom date range (requires customStartDate and customEndDate)
- `all`: All available data

**Features:**
- **MET Minutes Calculation**: Duration × MET Value based on activity and intensity
- **Calories Calculation**: MET Value × Patient Weight (kg) × Duration (hours)
- **Patient Weight Integration**: Automatically fetches patient weight from profile (defaults to 70kg)
- **Activity Distribution**: Pie chart data showing percentage breakdown by activity type
- **Intensity Distribution**: Pie chart data showing percentage breakdown by intensity level
- **Day-wise Charts**: Line/bar charts for MET minutes, duration, and stacked charts
- **Activity Records**: Detailed table of all physical activities with calculations

---

## **6\. Doctor APIs**

### **Get Doctor Profile**

**GET** /doctor?id={doctorId}

**Response:**

{  
  "id": "a1fdf239-109d-481d-9148-aa4e1fe38905",  
  "username": "encrypted-username",  
  "general": {  
    "fullName": "encrypted-name",  
    "designation": "Cardiologist",  
    "department": "Cardiology",  
    "doctorID": "DOC001",  
    "contactNumber": "+**********",  
    "workEmail": "<EMAIL>"  
  },  
  "personal": {  
    "age": 45,  
    "bloodGroup": "O+",  
    "height": 175,  
    "weight": 70,  
    "maritalStatus": "married",  
    "nationality": "Indian"  
  },  
  "professionalDetails": {  
    "medicalRegistration": {  
      "councilName": "Medical Council of India",  
      "registrationNumber": "MED123456",  
      "validFrom": "2010-01-01",  
      "validTo": "2030-01-01"  
    },  
    "specialties": \["Cardiology", "Internal Medicine"\],  
    "qualifications": \[  
      {  
        "degree": "MBBS",  
        "institution": "AIIMS Delhi",  
        "year": "2005"  
      },  
      {  
        "degree": "MD",  
        "specialization": "Cardiology",  
        "institution": "AIIMS Delhi",  
        "year": "2008"  
      }  
    \],  
    "experience": \[  
      {  
        "position": "Senior Cardiologist",  
        "hospital": "Apollo Hospital",  
        "duration": "2015-Present"  
      }  
    \]  
  },  
  "consultationFee": 1500,  
  "created\_on": "2024-01-01T00:00:00.000Z"  
}

### **Create Doctor**

**POST** /doctor

**Request Body:**

{  
  "general": {  
    "fullName": "Dr. John Smith",  
    "designation": "Cardiologist",  
    "department": "Cardiology",  
    "doctorID": "DOC002",  
    "contactNumber": "+**********",  
    "workEmail": "<EMAIL>"  
  },  
  "professionalDetails": {  
    "medicalRegistration": {  
      "councilName": "Medical Council of India",  
      "registrationNumber": "MED789012"  
    },  
    "qualifications": \[  
      {  
        "degree": "MBBS",  
        "institution": "Medical College",  
        "year": "2010"  
      }  
    \]  
  },  
  "consultationFee": 1000  
}

**Response:**

{  
  "id": "doctor-id",  
  "message": "Doctor profile created successfully",  
  "status": 201  
}

**PUT** /doctor

**Request Body:**

{  
  "id": "doctor-id",  
  "general": {  
    "designation": "Senior Cardiologist",  
    "contactNumber": "+**********"  
  },  
  "consultationFee": 1200  
}

**Response:**

{  
  "message": "Doctor profile updated successfully",  
  "status": 200  
}

**PATCH** /doctor

**Request Body:**

{  
  "id": "doctor-id",  
  "consultationFee": 1300  
}

**Response:**

{  
  "message": "Doctor profile updated successfully",  
  "status": 200  
}

**DELETE** /doctor?doctorId={doctorId}

**Response:**

{  
  "message": "Doctor profile deleted successfully",  
  "status": 200  
}

### **Doctor EMR Customization**

**GET** /doctor/customise-emr?doctorId={doctorId}

**Response:**

{  
  "id": "883a9250-9055-4397-9afc-6dfc0798730c",  
  "doctorId": "6a3e1c49-bcfc-4ef9-ae04-bc68c2d3f753",  
  "doc\_assist\_preference": "voice\_commands",  
  "preferred\_language\_for\_ambient\_listening": "English",  
  "selected\_tiles": \["vitals", "medications", "lab\_results"\],  
  "tile\_layout": "grid",  
  "medical\_note\_summary\_template": "SOAP format",  
  "extraNote1": "Summary",  
  "extraNote2": "Medication",  
  "extraNote3": "Notes",  
  "letterHeadDetails": "MBBS \\nMD in Surgery\\n33 years experience",  
  "organizationLogo": "https://ermdevstoragedata.blob.core.windows.net/file-uploads/user/6a3e1c49-bcfc-4ef9-ae04-bc68c2d3f753/document/logo.jpg",  
  "digitalSignature": "https://ermdevstoragedata.blob.core.windows.net/file-uploads/user/6a3e1c49-bcfc-4ef9-ae04-bc68c2d3f753/document/sign.png",  
  "created\_on": "2025-08-07T02:04:21.483Z",  
  "updated\_on": "2025-08-18T04:57:25.118Z"  
}

**POST** /doctor/customise-emr?doctorId={doctorId}

**Request Body:**

{  
  "doc\_assist\_preference": "text\_input",  
  "preferred\_language\_for\_ambient\_listening": "Hindi",  
  "selected\_tiles": \["patient\_history", "prescriptions", "appointments"\],  
  "tile\_layout": "list",  
  "medical\_note\_summary\_template": "Custom template",  
  "extraNote1": "Chief Complaint",  
  "extraNote2": "Assessment",  
  "extraNote3": "Plan",  
  "letterHeadDetails": "MBBS, MD\\nConsultant Physician\\n15 years experience"  
}

**Response:**

{  
  "id": "customization-id",  
  "message": "Doctor EMR customization created successfully",  
  "status": 201  
}

**PATCH** /doctor/customise-emr?id={id}

**Request Body:**

{  
  "preferred\_language\_for\_ambient\_listening": "English",  
  "selected\_tiles": \["vitals", "medications", "lab\_results", "imaging"\],  
  "extraNote1": "Updated Summary"  
}

**Response:**

{  
  "message": "Doctor EMR customization updated successfully",  
  "status": 200  
}

---

## **7\. Appointment APIs**

### **Get Appointments**

**GET** /appointment?doctorId={doctorId}\&date={date}\&patientId={patientId}

**Response:**

{  
  "appointments": \[  
    {  
      "id": "APM20240725624",  
      "doctorId": "7066645a-6c13-4443-8b71-1738fd55bed3",  
      "patientId": "patient-id",  
      "date": "2024-07-25",  
      "time": "10:00",  
      "department": "inPatient",  
      "status": "scheduled",  
      "duration": 30,  
      "appointmentType": "consultation",  
      "created\_on": "2024-07-25T12:16:38.672Z",  
      "updated\_on": "2025-01-22T16:37:10.977Z"  
    }  
  \]  
}

### **Create Appointment**

**POST** /appointment

**Request Body:**

{  
  "doctorId": "doctor-id",  
  "patientId": "patient-id",  
  "date": "2024-01-01",  
  "time": "10:00",  
  "department": "cardiology",  
  "duration": 30,  
  "appointmentType": "consultation",  
  "notes": "Regular checkup",  
  "priority": "normal"  
}

**Response:**

{  
  "id": "APM20240101001",  
  "message": "Appointment created successfully",  
  "status": 201  
}

### **Update Appointment**

**PUT** /appointment?doctorId={doctorId}

**Request Body:**

{  
  "appointmentId": "appointment-id",  
  "date": "2024-01-02",  
  "time": "11:00",  
  "status": "rescheduled",  
  "notes": "Patient requested reschedule"  
}

**Response:**

{  
  "message": "Appointment updated successfully",  
  "status": 200  
}

**PATCH** /appointment?appointmentId={appointmentId}

**Request Body:**

{  
  "status": "completed",  
  "actualDuration": 35,  
  "notes": "Consultation completed successfully"  
}

**Response:**

{  
  "message": "Appointment updated successfully",  
  "status": 200  
}

### **Delete Appointment**

**DELETE** /appointment?appointmentId={appointmentId}

**Response:**

{  
  "message": "Appointment deleted successfully",  
  "status": 200  
}

### **Future Appointments**

**GET** /book-consultation/future?patientId={patientId}

**Response:**

{  
  "patientId": "patient-id",  
  "futureAppointments": \[  
    {  
      "id": "appointment-id",  
      "doctorId": "doctor-id",  
      "doctorName": "Dr. John Smith",  
      "date": "2024-01-15",  
      "time": "14:00",  
      "department": "Cardiology",  
      "status": "scheduled",  
      "appointmentType": "follow-up"  
    }  
  \],  
  "totalCount": 3  
}

---

## **8\. Medicine APIs**

**Base url: [*************************************/medicine/v0.1](*************************************/medicine/v0.1)**

### **Get All Medicines**

**GET** /medicine

**Response:**

{  
  "medicines": \[  
    {  
      "id": "med-001",  
      "name": "Paracetamol",  
      "genericName": "Acetaminophen",  
      "strength": "500mg",  
      "dosageForm": "Tablet",  
      "manufacturer": "Cipla Ltd",  
      "price": 10.50,  
      "isActive": **true**,  
      "category": "Analgesic"  
    }  
  \],  
  "totalCount": 5000  
}

### **Search Medicines**

**POST** /medicine/search?organizationId={organizationId}

**Request Body:**

{  
  "searchText": "paracetamol",  
  "pageSize": 10,  
  "continuationToken": "token",  
  "page": 1,  
  "filters": {  
    "category": "Analgesic",  
    "manufacturer": "Cipla",  
    "isActive": **true**  
  }  
}

**Response:**

{  
  "medicines": \[  
    {  
      "id": "med-001",  
      "name": "Paracetamol 500mg",  
      "genericName": "Acetaminophen",  
      "strength": "500mg",  
      "dosageForm": "Tablet",  
      "manufacturer": "Cipla Ltd",  
      "price": 10.50,  
      "organizationPrice": 12.00,  
      "isActive": **true**,  
      "category": "Analgesic",  
      "productForm": "Tab"  
    }  
  \],  
  "continuationToken": "next-token",  
  "hasMoreResults": **true**,  
  "totalFetched": 10,  
  "currentPage": 1,  
  "totalPages": 50  
}

---

## **9\. Lab Test APIs**

**Base url : [*************************************/lab-test/v0.1](*************************************/lab-test/v0.1)**

### **Get All Lab Tests**

**GET** /lab-tests

**Response:**

{  
  "labTests": \[  
    {  
      "id": "test-001",  
      "name": "Complete Blood Count",  
      "category": "Hematology",  
      "price": 300,  
      "isActive": **true**,  
      "sampleType": "Blood",  
      "reportTime": "24 hours"  
    }  
  \],  
  "totalCount": 1000  
}

### **Add Lab Test**

**POST** /lab-tests

**Request Body:**

{  
  "name": "Lipid Profile",  
  "category": "Biochemistry",  
  "price": 500,  
  "sampleType": "Blood",  
  "reportTime": "12 hours",  
  "parameters": \[  
    "Total Cholesterol",  
    "HDL Cholesterol",  
    "LDL Cholesterol",  
    "Triglycerides"  
  \]  
}

**Response:**

{  
  "id": "test-002",  
  "message": "Lab test added successfully",  
  "status": 201  
}

### **Search Lab Tests**

**POST** /lab-tests/search

**Request Body:**

{  
  "searchText": "blood test",  
  "pageSize": 10,  
  "continuationToken": "token",  
  "filters": {  
    "category": "Hematology",  
    "isActive": **true**  
  }  
}

**Response:**

{  
  "labTests": \[  
    {  
      "id": "test-001",  
      "name": "Complete Blood Count",  
      "category": "Hematology",  
      "price": 300,  
      "isActive": **true**,  
      "sampleType": "Blood"  
    }  
  \],  
  "continuationToken": "next-token",  
  "hasMoreResults": **true**,  
  "totalFetched": 10  
}

### **Get Lab Test Departments**

**GET** /lab-test/departments

**Response:**

{  
  "departments": \[  
    {  
      "name": "Hematology",  
      "testCount": 25  
    },  
    {  
      "name": "Biochemistry",  
      "testCount": 45  
    },  
    {  
      "name": "Microbiology",  
      "testCount": 30  
    }  
  \]  
}

### **Get LOINC List**

**GET** /loinc/list?searchText={searchText}\&department={department}\&organizationId={organizationId}\&pageSize={pageSize}\&continuationToken={token}\&page={page}\&status={status}

**Response:**

{  
  "loincTests": \[  
    {  
      "id": "9999-4",  
      "LOINC\_NUM": "9999-4",  
      "COMPONENT": "R wave duration.lead AVL",  
      "PROPERTY": "Time",  
      "TIME\_ASPCT": "Pt",  
      "SYSTEM": "Heart",  
      "SCALE\_TYP": "Qn",  
      "METHOD\_TYP": "EKG",  
      "CLASS": "EKG.MEAS",  
      "SHORTNAME": "R wave dur L-AVL",  
      "LONG\_COMMON\_NAME": "R wave duration in lead AVL",  
      "EXAMPLE\_UNITS": "ms",  
      "STATUS": "ACTIVE",  
      "organizationPrice": 150.00,  
      "isActive": **true**  
    }  
  \],  
  "continuationToken": "next-token",  
  "hasMoreResults": **true**,  
  "totalFetched": 10,  
  "currentPage": 1,  
  "totalPages": 1000  
}

### **Update LOINC**

**POST** /loinc/update

**Request Body:**

{  
  "organizationId": "org-id",  
  "updates": \[  
    {  
      "loincId": "9999-4",  
      "isActive": **true**,  
      "price": 200.00  
    },  
    {  
      "loincId": "1000-5",  
      "isActive": **false**,  
      "price": 0  
    }  
  \]  
}

**Response:**

{  
  "message": "LOINC tests updated successfully",  
  "updatedCount": 2,  
  "failedCount": 0,  
  "status": 200  
}

### **LOINC Tests for Organization**

**GET** /loinc/tests-for-organization?organizationId={organizationId}\&pageSize={pageSize}\&continuationToken={token}

**Response:**

{  
  "organizationTests": \[  
    {  
      "loincId": "9999-4",  
      "testName": "R wave duration in lead AVL",  
      "price": 150.00,  
      "isActive": **true**,  
      "organizationId": "org-id"  
    }  
  \],  
  "continuationToken": "next-token",  
  "hasMoreResults": **true**,  
  "totalFetched": 50  
}

### **Patient Lab Tests**

**GET** /patient-lab-test?patientId={patientId}

**Response:**

{  
  "patientId": "patient-id",  
  "labTests": \[  
    {  
      "id": "lab-test-id",  
      "patientId": "patient-id",  
      "doctorId": "doctor-id",  
      "testDate": "2024-01-01",  
      "status": "completed",  
      "labTests": \[  
        {  
          "testId": "test-001",  
          "testName": "Complete Blood Count",  
          "results": "Normal",  
          "reference": "4.5-11.0 x10^3/uL",  
          "status": "ready"  
        }  
      \],  
      "totalAmount": 500,  
      "paymentStatus": "paid"  
    }  
  \]  
}

**POST** /patient-lab-test

**Request Body:**

{  
  "patientId": "patient-id",  
  "doctorId": "doctor-id",  
  "testDate": "2024-01-01",  
  "labTests": \[  
    {  
      "testId": "test-001",  
      "testName": "Complete Blood Count",  
      "price": 300  
    },  
    {  
      "testId": "test-002",  
      "testName": "Lipid Profile",  
      "price": 500  
    }  
  \],  
  "notes": "Routine checkup tests"  
}

**Response:**

{  
  "id": "lab-test-id",  
  "message": "Patient lab test created successfully",  
  "totalAmount": 800,  
  "status": 201  
}

**PATCH** /patient-lab-test

**Request Body:**

{  
  "id": "lab-test-id",  
  "status": "completed",  
  "labTests": \[  
    {  
      "testId": "test-001",  
      "results": "Hemoglobin: 12.5 g/dL",  
      "reference": "12.0-15.5 g/dL",  
      "status": "ready"  
    }  
  \]  
}

**Response:**

{  
  "message": "Patient lab test updated successfully",  
  "status": 200  
}

**DELETE** /patient-lab-test

**Request Body:**

{  
  "id": "lab-test-id"  
}

**Response:**

{  
  "message": "Patient lab test deleted successfully",  
  "status": 200  
}

---

## **10\. Test Package APIs**

### **Get Test Packages**

**Base url : [*************************************/test-package/v0.1](*************************************/test-package/v0.1)**

**GET** /test-package?type={type}

**Response:**

{  
  "testPackages": \[  
    {  
      "id": "package-001",  
      "name": "Basic Health Checkup",  
      "type": "routine",  
      "description": "Comprehensive basic health screening package",  
      "price": 1500,  
      "discountedPrice": 1200,  
      "tests": \[  
        {  
          "testId": "test-001",  
          "testName": "Complete Blood Count",  
          "price": 300  
        },  
        {  
          "testId": "test-002",  
          "testName": "Lipid Profile",  
          "price": 500  
        },  
        {  
          "testId": "test-003",  
          "testName": "Blood Sugar",  
          "price": 200  
        }  
      \],  
      "isActive": **true**,  
      "validityDays": 30  
    }  
  \]  
}

### **Create Test Package**

**POST** /test-package

**Request Body:**

{  
  "name": "Cardiac Health Package",  
  "type": "specialized",  
  "description": "Comprehensive cardiac health assessment",  
  "tests": \[  
    {  
      "testId": "test-004",  
      "testName": "ECG",  
      "price": 200  
    },  
    {  
      "testId": "test-005",  
      "testName": "Echo Cardiogram",  
      "price": 1500  
    },  
    {  
      "testId": "test-002",  
      "testName": "Lipid Profile",  
      "price": 500  
    }  
  \],  
  "originalPrice": 2200,  
  "discountedPrice": 1800,  
  "validityDays": 45,  
  "isActive": **true**  
}

**Response:**

{  
  "id": "package-002",  
  "message": "Test package created successfully",  
  "status": 201  
}

### **Update Test Package**

**PATCH** /test-package?packageId={packageId}

**Request Body:**

{  
  "name": "Updated Cardiac Health Package",  
  "discountedPrice": 1700,  
  "validityDays": 60,  
  "tests": \[  
    {  
      "testId": "test-004",  
      "testName": "ECG",  
      "price": 200  
    },  
    {  
      "testId": "test-005",  
      "testName": "Echo Cardiogram",  
      "price": 1500  
    },  
    {  
      "testId": "test-002",  
      "testName": "Lipid Profile",  
      "price": 500  
    },  
    {  
      "testId": "test-006",  
      "testName": "Stress Test",  
      "price": 800  
    }  
  \]  
}

**Response:**

{  
  "message": "Test package updated successfully",  
  "status": 200  
}

### **Get Tests for Package**

**GET** /package/tests?packageId={packageId}

**Response:**

{  
  "packageId": "package-001",  
  "packageName": "Basic Health Checkup",  
  "tests": \[  
    {  
      "testId": "test-001",  
      "testName": "Complete Blood Count",  
      "category": "Hematology",  
      "price": 300,  
      "sampleType": "Blood",  
      "reportTime": "24 hours"  
    },  
    {  
      "testId": "test-002",  
      "testName": "Lipid Profile",  
      "category": "Biochemistry",  
      "price": 500,  
      "sampleType": "Blood",  
      "reportTime": "12 hours"  
    }  
  \],  
  "totalTests": 3,  
  "totalPrice": 1000,  
  "packagePrice": 800  
}

---

## **11\. ABDM (ABHA) APIs**

**Base url : [*************************************/abdm/v0.1](*************************************/abdm/v0.1)**

### **ABDM Operations**

**POST** /abdm?operation={operation}

#### *Initiate ABHA Creation with Aadhaar*

**POST** /abdm?operation=initiate-aadhaar

**Request Body:**

{  
  "aadhaarNumber": "**********12",  
  "consent": **true**  
}

**Response:**

{  
  "txnId": "transaction-id",  
  "message": "OTP sent to registered mobile number",  
  "status": 200  
}

#### *Initiate ABHA Creation with Mobile*

**POST** /abdm/initiate/mobile

**Request Body:**

{  
  "mobileNumber": "+************",  
  "consent": **true**  
}

**Response:**

{  
  "txnId": "transaction-id",  
  "message": "OTP sent to mobile number",  
  "status": 200  
}

#### *Verify OTP*

**POST** /abdm/verify-otp

**Request Body:**

{

 "txnId": "718fcdf0-ed11-412d-a778-3408ef6327f9",

 "otp": "728606",

 "type": "aadhaar",

 "mobile":"7034302533"

}

**Response:**

{  
  "txnId": "transaction-id",  
  "message": "OTP verified successfully",  
  "authToken": "auth-token",  
  "status": 200  
}

#### *Get ABHA Details by Number*

**POST** /abdm/details/by-number

**Request Body:**

{  
  "abhaNumber": "12-**************"  
}

**Response:**

{  
  "abhaNumber": "12-**************",  
  "abhaAddress": "john.doe@abdm",  
  "name": "John Doe",  
  "gender": "M",  
  "dateOfBirth": "1990-01-01",  
  "mobile": "+************",  
  "email": "<EMAIL>",  
  "address": "123 Main Street, City, State",  
  "status": "active"  
}

#### *Get ABHA Details by Mobile*

**POST** /abdm?operation=details-by-mobile

**Request Body:**

{  
  "mobileNumber": "+************"  
}

**Response:**

{  
  "abhaNumbers": \[  
    {  
      "abhaNumber": "12-**************",  
      "abhaAddress": "john.doe@abdm",  
      "name": "John Doe",  
      "status": "active"  
    }  
  \]  
}

#### *Verify OTP and Fetch Details*

**POST** /abdm/verify-otp-fetch-details

**Request Body:**

{

   "txnId":"c3b1fc70-ca4b-426e-b421-46deadab7298",

   "otp":"902537"

}

**Response:**

{  
  "abhaNumber": "12-**************",  
  "name": "John Doe",  
  "gender": "M",  
  "dateOfBirth": "1990-01-01",  
  "mobile": "+************",  
  "authToken": "auth-token",  
  "status": 200  
}

#### *Request OTP for Mobile Profile*

**POST** /abdm?operation=request-otp-mobile-profile

**Request Body:**

{  
  "abhaNumber": "12-**************",  
  "mobileNumber": "+************"  
}

**Response:**

{  
  "txnId": "transaction-id",  
  "message": "OTP sent for profile verification",  
  "status": 200  
}

#### *Verify OTP and Fetch Details by Mobile*

**POST** /abdm?operation=verify-otp-fetch-details-by-mobile

**Request Body:**

{  
  "txnId": "transaction-id",  
  "otp": "123456",  
  "mobileNumber": "+************"  
}

**Response:**

{  
  "abhaNumber": "12-**************",  
  "abhaAddress": "john.doe@abdm",  
  "name": "John Doe",  
  "gender": "M",  
  "dateOfBirth": "1990-01-01",  
  "mobile": "+************",  
  "email": "<EMAIL>",  
  "status": 200  
}

---

## **12\. Lab Report Document APIs**

### **Upload Lab Report**

**POST** /lab-report/upload *Content-Type: multipart/form-data*

**Request Body:**

files: \[lab-report.pdf, lab-report2.pdf\]  
patientId: "**********"  
labTestId: "9e951ca4-70b5-407e-baa9-908d3235b56f"

**Response:**

{  
  "message": "Upload successful",  
  "metadata": \[  
    {  
      "id": "64dbab04-7f55-44ff-9435-533279263b1e",  
      "fileName": "lab\_results.pdf",  
      "fileSize": 2058,  
      "blobPath": "patients/**********/labtest/9e951ca4-70b5-407e-baa9-908d3235b56f/64dbab04-7f55-44ff-9435-533279263b1e-lab\_results.pdf",  
      "patientId": "**********",  
      "labTestId": "9e951ca4-70b5-407e-baa9-908d3235b56f",  
      "ocrStatus": "completed",  
      "detectedLanguage": **null**,  
      "ocrData": {  
        "raw\_ocr": \[  
          {  
            "page\_number": 1,  
            "blocks": \[  
              {  
                "type": "paragraph",  
                "content": "Test Name Result Unit Reference Interval"  
              },  
              {  
                "type": "key\_value",  
                "key": "B lymphocytes (Bld) 168.0 \#/Vol Normal",  
                "value": "\<150"  
              }  
            \]  
          }  
        \],  
        "flattened": "\[TEST\] Test Name Result Unit Reference Interval\\n\[FIELD\] B lymphocytes (Bld) 168.0 \#/Vol Normal: \<150",  
        "structured": {  
          "patient": {  
            "name": **null**,  
            "sex": **null**,  
            "age": **null**,  
            "date\_of\_birth": **null**  
          },  
          "doctor": {  
            "name": **null**  
          },  
          "date\_of\_test": **null**,  
          "lab\_or\_facility": "Not specified",  
          "test\_results": \[  
            {  
              "test\_name": "B lymphocytes (Bld)",  
              "value": "168.0",  
              "unit": "\#/Vol",  
              "reference\_interval": "\<150"  
            },  
            {  
              "test\_name": "R wave dur L-V2",  
              "value": "145.0",  
              "unit": "ms",  
              "reference\_interval": "\<200"  
            }  
          \]  
        }  
      },  
      "fileType": "application/pdf",  
      "uploadedAt": "2025-08-14T07:19:00.414Z"  
    }  
  \]  
}

### **Preview Lab Report**

**GET** /lab-report/preview?docId={docId}

**Response:**

Content-Type: application/pdf  
Body: \[PDF file content as binary data\]

---

## **13\. Nutrition APIs**

### 

**Base url : [*************************************/nutrition/v0.1](*************************************/nutrition/v0.1)**

### **Get Food List**

**GET** /food/list?input={searchTerm}

**Response:**

\[  
  {  
    "food\_name": "Hot tea (Garam Chai)"  
  },  
  {  
    "food\_name": "Tea biscuit"  
  },  
  {  
    "food\_name": "Iced tea"  
  }  
\]

### **Get Food Serving Unit**

**GET** /food/serving-unit?foodName={foodName}

**Response:**

{  
  "servings\_unit": "tea cup"  
}

### **Get Patient Nutrition Summary**

**GET** /patient/nutrition/summary?patientId={patientId}&filter={filter}&metrics={metrics}

**Query Parameters:**
- `patientId` (required): Patient identifier
- `filter` (optional): Time period filter (last_7_days, last_15_days, last_month) - defaults to "last_7_days"
- `metrics` (optional): macro or micro
- `sort` : asc or desc - default to ascending

**Response:**

\[  
  {  
    "date": "2025-08-15",  
    "calories": 625.16,  
    "carbs": 86.56,  
    "protein": 20.12,  
    "fat": 22.18,  
    "sugar": 40.48,  
    "salt": 0.32,  
    "sfa": 13164,  
    "mufa": 6082.2,  
    "pufa": 1008.6,  
    "fiber": 2.08,  
    "cholesterol": 0,  
    "fat\_percentage": 17.21,  
    "carbs\_percentage": 67.17,  
    "protein\_percentage": 15.61,  
    "meals": 1,  
    "snacks": 0  
  },  
  {  
    "date": "2025-08-14",  
    "calories": 457.56,  
    "carbs": 60.73,  
    "protein": 15.23,  
    "fat": 17.5,  
    "sugar": 30.58,  
    "salt": 0.24,  
    "sfa": 10093.75,  
    "mufa": 4869.6,  
    "pufa": 995.8,  
    "fiber": 1.82,  
    "cholesterol": 0.03,  
    "fat\_percentage": 18.72,  
    "carbs\_percentage": 64.98,  
    "protein\_percentage": 16.3,  
    "meals": 2,  
    "snacks": 0  
  }  
\]

### **Get Patient Nutrition Average**

**GET** /patient/nutrition/average?patientId={patientId}&filter={filter}

**Query Parameters:**
- `patientId` (required): Patient identifier
- `filter` (optional): Time period filter (last_7_days, last_15_days, last_month) - defaults to "last_7_days"

**Response:**

{  
  "filter": "last_7_days",  
  "fromDate": "2025-08-14",  
  "toDate": "2025-08-20",  
  "daysWithData": 2,  
  "averages": {  
    "calories": 541.36,  
    "carbs": 73.65,  
    "protein": 17.68,  
    "fat": 19.84,  
    "fiber": 1.95,  
    "sugar": 35.53,  
    "salt": 0.28  
  }  
}

### **Get Patient Nutrition Chart Data**

**GET** /patient/nutrition/chart?patientId={patientId}&filter={filter}&metric={metric}

**Query Parameters:**
- `patientId` (required): Patient identifier
- `filter` (optional): Time period filter (last_7_days, last_15_days, last_month) - defaults to "last_7_days"
- `metric` (optional): Specific metric to chart - defaults to "carbs"
  - **MacroChart metrics**: calories, carbs, protein, fat, fiber
  - **Micro metrics**: calcium, magnesium, phosphorus, iron, sodium, potassium
  - **Additional metrics**: salt, sugar
  - **Special values**: micro (all micro metrics), additional (all additional metrics)

**Response:**

{  
  "filter": "last_7_days",  
  "metrics": \["carbs"\],  
  "fromDate": "2025-08-14",  
  "toDate": "2025-08-20",  
  "chartData": \[  
    {  
      "date": "2025-08-14",  
      "carbs": 45.2  
    },  
    {  
      "date": "2025-08-15",  
      "carbs": 38.7  
    },  
    {  
      "date": "2025-08-16",  
      "carbs": 42.1  
    }  
  \]  
}

### **Get Patient Nutrition Percentage Chart**

**GET** /patient/nutrition/percentage-chart?patientId={patientId}&filter={filter}

**Query Parameters:**
- `patientId` (required): Patient identifier
- `filter` (optional): Time period filter (last_7_days, last_15_days, last_month) - defaults to "last_7_days"

**Response:**

{  
  "filter": "last_7_days",  
  "fromDate": "2025-08-14",  
  "toDate": "2025-08-20",  
  "chartData": \[  
    {  
      "date": "2025-08-14",  
      "fat\_percentage": 18.72,  
      "carbs\_percentage": 64.98,  
      "protein\_percentage": 16.3  
    },  
    {  
      "date": "2025-08-15",  
      "fat\_percentage": 17.21,  
      "carbs\_percentage": 67.17,  
      "protein\_percentage": 15.61  
    },  
    {  
      "date": "2025-08-16",  
      "fat\_percentage": 0,  
      "carbs\_percentage": 0,  
      "protein\_percentage": 0  
    },  
    {  
      "date": "2025-08-17",  
      "fat\_percentage": 0,  
      "carbs\_percentage": 0,  
      "protein\_percentage": 0  
    },  
    {  
      "date": "2025-08-18",  
      "fat\_percentage": 0,  
      "carbs\_percentage": 0,  
      "protein\_percentage": 0  
    },  
    {  
      "date": "2025-08-19",  
      "fat\_percentage": 0,  
      "carbs\_percentage": 0,  
      "protein\_percentage": 0  
    },  
    {  
      "date": "2025-08-20",  
      "fat\_percentage": 0,  
      "carbs\_percentage": 0,  
      "protein\_percentage": 0  
    }  
  \]  
}

---

## **14\. EMR Customization APIs**

### **Get EMR Customization**

**GET** /customise-emr?source\_name={sourceName}

**Response:**

{  
  "id": "5ccd8412-6909-48a6-9f93-6cd43331eba0",  
  "source\_name": "miscellaneous\_clinical\_metrics",  
  "data": \[  
    {  
      "key": "Pain Scale Score (0-10)",  
      "value": "Pain Scale Score (0-10)",  
      "abbreviationCode": "PSS",  
      "unit": "score"  
    },  
    {  
      "key": "Fatigue Severity Score (FSS)",  
      "value": "Fatigue Severity Score (FSS)",  
      "abbreviationCode": "FSS",  
      "unit": "score"  
    },  
    {  
      "key": "Physical Activity Level (METs)",  
      "value": "Physical Activity Level (METs)",  
      "abbreviationCode": "PAL",  
      "unit": "METs"  
    }  
  \],  
  "created\_on": "2025-02-23T07:48:58.355Z",  
  "updated\_on": "2025-02-23T07:48:58.355Z"  
}

### **Create EMR Customization**

**POST** /customise-emr

**Request Body:**

{  
  "source\_name": "vital\_signs\_custom",  
  "data": \[  
    {  
      "key": "Blood Oxygen Level",  
      "value": "Blood Oxygen Level (SpO2)",  
      "abbreviationCode": "SpO2",  
      "unit": "%"  
    },  
    {  
      "key": "Body Mass Index",  
      "value": "Body Mass Index",  
      "abbreviationCode": "BMI",  
      "unit": "kg/m²"  
    }  
  \]  
}

**Response:**

{  
  "id": "customization-id",  
  "message": "EMR customization created successfully",  
  "status": 201  
}

### **Update EMR Customization**

**PATCH** /customise-emr?id={id}

**Request Body:**

{  
  "data": \[  
    {  
      "key": "Blood Oxygen Level",  
      "value": "Blood Oxygen Saturation (SpO2)",  
      "abbreviationCode": "SpO2",  
      "unit": "%"  
    },  
    {  
      "key": "Respiratory Rate",  
      "value": "Respiratory Rate",  
      "abbreviationCode": "RR",  
      "unit": "breaths/min"  
    }  
  \]  
}

**Response:**

{  
  "message": "EMR customization updated successfully",  
  "status": 200  
}

---

## **15\. Payment APIs**

**Base url : [*************************************/payment/v0.1](*************************************/payment/v0.1)**

### **Create Payment Order**

**POST** /payments/create-order

**Request Body:**

{  
  "amount": 1500,  
  "currency": "INR",  
  "patientId": "patient-id",  
  "paymentType": "consultation",  
  "description": "Doctor consultation fee",  
  "doctorId": "doctor-id",  
  "appointmentId": "appointment-id"  
}

**Response:**

{  
  "orderId": "order\_MN**********1234",  
  "amount": 1500,  
  "currency": "INR",  
  "key": "rzp\_test\_CUHJLaG7X7H8uG",  
  "name": "EMR Healthcare",  
  "description": "Doctor consultation fee",  
  "prefill": {  
    "name": "Patient Name",  
    "email": "<EMAIL>",  
    "contact": "+************"  
  },  
  "status": "created"  
}

### **Verify Payment**

**POST** /payments/verify

**Request Body:**

{  
  "razorpay\_order\_id": "order\_MN**********1234",  
  "razorpay\_payment\_id": "pay\_MN**********1234",  
  "razorpay\_signature": "signature\_hash"  
}

**Response:**

{  
  "status": "success",  
  "message": "Payment verified successfully",  
  "paymentId": "pay\_MN**********1234",  
  "orderId": "order\_MN**********1234",  
  "amount": 1500,  
  "paymentStatus": "completed"  
}

### **Payment Webhook**

**POST** /payments/webhook *Auth Level: anonymous*

**Request Body:**

{  
  "entity": "event",  
  "account\_id": "acc\_BFQ7uQEaa30GJy",  
  "event": "payment.captured",  
  "contains": \["payment"\],  
  "payload": {  
    "payment": {  
      "entity": {  
        "id": "pay\_MN**********1234",  
        "amount": 150000,  
        "currency": "INR",  
        "status": "captured",  
        "order\_id": "order\_MN**********1234"  
      }  
    }  
  },  
  "created\_at": **********  
}

**Response:**

{  
  "status": "success",  
  "message": "Webhook processed successfully"  
}

### **Get Payment Details**

**GET** /payments/details?paymentId={paymentId}

**Response:**

{  
  "id": "payment-id",  
  "razorpayPaymentId": "pay\_MN**********1234",  
  "razorpayOrderId": "order\_MN**********1234",  
  "amount": 1500,  
  "currency": "INR",  
  "status": "completed",  
  "paymentType": "consultation",  
  "patientId": "patient-id",  
  "doctorId": "doctor-id",  
  "appointmentId": "appointment-id",  
  "created\_on": "2024-01-01T10:00:00.000Z",  
  "completed\_on": "2024-01-01T10:05:00.000Z"  
}

---

## **16\. Role & Permission APIs**

**Base url: [*************************************/role/v0.1](*************************************/role/v0.1)**

### **List Roles**

**GET** /list-roles?organizationId={organizationId}

**Response:**

{  
  "roles": \[  
    {  
      "id": "role-001",  
      "roleName": "Doctor",  
      "organizationId": "org-id",  
      "permissions": \["emr.access", "patient.view", "patient.manage"\],  
      "isActive": **true**,  
      "created\_on": "2024-01-01T00:00:00.000Z"  
    },  
    {  
      "id": "role-002",  
      "roleName": "Nurse",  
      "organizationId": "org-id",  
      "permissions": \["emr.access", "patient.view", "vitals.manage"\],  
      "isActive": **true**,  
      "created\_on": "2024-01-01T00:00:00.000Z"  
    }  
  \]  
}

### **Get Role**

**GET** /role?roleId={roleId}

**Response:**

{  
  "id": "role-001",  
  "roleName": "Doctor",  
  "organizationId": "org-id",  
  "description": "Medical doctor with full patient access",  
  "permissions": \[  
    {  
      "permissionKey": "emr.access",  
      "description": "Access to EMR system"  
    },  
    {  
      "permissionKey": "patient.view",  
      "description": "View patient information"  
    },  
    {  
      "permissionKey": "patient.manage",  
      "description": "Manage patient records"  
    }  
  \],  
  "isActive": **true**,  
  "created\_on": "2024-01-01T00:00:00.000Z"  
}

### **Create Role**

**POST** /role

**Request Body:**

{  
  "roleName": "Lab Technician",  
  "organizationId": "org-id",  
  "description": "Laboratory technician role",  
  "permissions": \[  
    "emr.access",  
    "lab.view",  
    "lab.manage",  
    "lab.report.upload"  
  \]  
}

**Response:**

{  
  "id": "role-003",  
  "message": "Role created successfully",  
  "status": 201  
}

### **Update Role**

**PATCH** /role?roleId={roleId}

**Request Body:**

{  
  "roleName": "Senior Lab Technician",  
  "description": "Senior laboratory technician with additional permissions",  
  "permissions": \[  
    "emr.access",  
    "lab.view",  
    "lab.manage",  
    "lab.report.upload",  
    "lab.report.approve"  
  \]  
}

**Response:**

{  
  "message": "Role updated successfully",  
  "status": 200  
}

### **Delete Role**

**DELETE** /role?roleId={roleId}

**Response:**

{  
  "message": "Role deleted successfully",  
  "status": 200  
}

### **Assign Permissions**

**POST** /assign-permissions

**Request Body:**

{  
  "roleId": "role-001",  
  "permissions": \[  
    "emr.access",  
    "patient.view",  
    "patient.manage",  
    "prescription.create"  
  \]  
}

**Response:**

{  
  "message": "Permissions assigned successfully",  
  "assignedCount": 4,  
  "status": 200  
}

### **Get API List by Role**

**GET** /permissions/api-list?roleId={roleId}

**Response:**

{  
  "roleId": "role-001",  
  "roleName": "Doctor",  
  "allowedAPIs": \[  
    {  
      "api": "patient",  
      "methods": \["GET", "POST", "PATCH"\],  
      "permissionKey": "patient.manage"  
    },  
    {  
      "api": "patient/history",  
      "methods": \["GET", "POST", "PUT"\],  
      "permissionKey": "emr.access"  
    },  
    {  
      "api": "patient/vitals",  
      "methods": \["GET", "POST", "PUT"\],  
      "permissionKey": "emr.access"  
    },  
    {  
      "api": "prescription",  
      "methods": \["GET", "POST"\],  
      "permissionKey": "prescription.create"  
    }  
  \]  
}

## **17\. Proxy APIs**

### **ICD Proxy**

**GET** /icd-proxy?q={searchTerm}

**Response:**

{  
  "destinationEntities": \[  
    {  
      "id": "http://id.who.int/icd/entity/455013390",  
      "title": "Essential hypertension",  
      "definition": "A condition of sustained elevation of systemic arterial blood pressure",  
      "longDefinition": "Essential hypertension is high blood pressure that doesn't have a known secondary cause",  
      "fullySpecifiedName": "Essential hypertension",  
      "source": "ICD-11 MMS",  
      "code": "BA00",  
      "codingNote": "Use this code for primary hypertension"  
    }  
  \]  
}

### **SNOMED Proxy**

**GET** /snomed-proxy?term={searchTerm}

**Response:**

{  
  "items": \[  
    {  
      "concept": {  
        "id": "38341003",  
        "fsn": {  
          "term": "Hypertensive disorder, systemic arterial (disorder)"  
        },  
        "pt": {  
          "term": "High blood pressure"  
        },  
        "definitionStatus": "FULLY\_DEFINED"  
      },  
      "conceptId": "38341003",  
      "term": "High blood pressure",  
      "active": **true**  
    }  
  \],  
  "total": 1  
}

---

## **18\. Summary APIs**

### **Consultation Summary**

**POST** /summary *Auth Level: anonymous* *Content-Type: text/plain*

**Request Body:**

Doctor: How are you feeling today? Patient: I have been experiencing chest pain and shortness of breath for the past two days. Doctor: Can you describe the pain? Patient: It's a sharp pain that comes and goes, especially when I exert myself. Doctor: Any other symptoms? Patient: Yes, I also feel dizzy sometimes and have been very tired.

**Response:**

{  
  "conversation": \[  
    {  
      "speaker": "doctor",  
      "text": "How are you feeling today?"  
    },  
    {  
      "speaker": "patient",  
      "text": "I have been experiencing chest pain and shortness of breath for the past two days"  
    },  
    {  
      "speaker": "doctor",  
      "text": "Can you describe the pain?"  
    },  
    {  
      "speaker": "patient",  
      "text": "It's a sharp pain that comes and goes, especially when I exert myself"  
    },  
    {  
      "speaker": "doctor",  
      "text": "Any other symptoms?"  
    },  
    {  
      "speaker": "patient",  
      "text": "Yes, I also feel dizzy sometimes and have been very tired"  
    }  
  \],  
  "summary": {  
    "chief\_complaint": "Chest pain and shortness of breath",  
    "duration": "2 days",  
    "symptoms": \[  
      "chest pain",  
      "shortness of breath",  
      "dizziness",  
      "fatigue"  
    \],  
    "pain\_characteristics": "Sharp, intermittent, exertional",  
    "associated\_symptoms": "Dizziness, fatigue",  
    "diagnosis": **null**,  
    "treatment": **null**,  
    "follow\_up": **null**  
  }  
}

## **19\. Prescription APIs**

**Base url : [*************************************/prescription-package/v0.1](*************************************/prescription-package/v0.1)**

### **Get Prescriptions**

**GET** /prescriptions?patientId={patientId}\&doctorId={doctorId}

**Response:**

{  
  "prescriptions": \[  
    {  
      "id": "prescription-id",  
      "patientId": "patient-id",  
      "doctorId": "doctor-id",  
      "prescriptionDate": "2024-01-01",  
      "medicines": \[  
        {  
          "medicineId": "med-001",  
          "medicineName": "Paracetamol 500mg",  
          "dosage": "1 tablet",  
          "frequency": "twice daily",  
          "duration": "5 days",  
          "instructions": "Take after meals"  
        },  
        {  
          "medicineId": "med-002",  
          "medicineName": "Amoxicillin 250mg",  
          "dosage": "1 capsule",  
          "frequency": "three times daily",  
          "duration": "7 days",  
          "instructions": "Complete the course"  
        }  
      \],  
      "diagnosis": "Upper respiratory tract infection",  
      "notes": "Follow up if symptoms persist",  
      "totalAmount": 150.00,  
      "status": "active"  
    }  
  \]  
}

### **Prescription Package APIs**

**Base url : [*************************************/prescription-package/v0.1](*************************************/prescription-package/v0.1)**

**GET** /prescription-package

**Response:**

{  
  "prescriptionPackages": \[  
    {  
      "id": "package-001",  
      "name": "Diabetes Management Package",  
      "description": "Complete diabetes care package",  
      "medicines": \[  
        {  
          "medicineId": "med-003",  
          "medicineName": "Metformin 500mg",  
          "quantity": 30,  
          "price": 50.00  
        },  
        {  
          "medicineId": "med-004",  
          "medicineName": "Glimepiride 2mg",  
          "quantity": 30,  
          "price": 80.00  
        }  
      \],  
      "totalPrice": 130.00,  
      "discountedPrice": 110.00,  
      "isActive": **true**  
    }  
  \]  
}

**POST** /prescription-package

**Request Body:**

{  
  "name": "Hypertension Care Package",  
  "description": "Complete hypertension management package",  
  "medicines": \[  
    {  
      "medicineId": "med-005",  
      "medicineName": "Amlodipine 5mg",  
      "quantity": 30,  
      "price": 60.00  
    },  
    {  
      "medicineId": "med-006",  
      "medicineName": "Atenolol 50mg",  
      "quantity": 30,  
      "price": 40.00  
    }  
  \],  
  "totalPrice": 100.00,  
  "discountedPrice": 85.00  
}

**Response:**

{  
  "id": "package-002",  
  "message": "Prescription package created successfully",  
  "status": 201  
}

---

## **20\. Additional APIs**

### **Queue Management**

**GET** /queue?departmentId={departmentId} **POST** /queue **PATCH** /queue?queueId={queueId}

### **Appointment Queue**

**GET** /appointment-queue?doctorId={doctorId}\&date={date}

### **Doctor Summary**

**GET** /doctor-summary?doctorId={doctorId}\&startDate={startDate}\&endDate={endDate}

---

## **Data Models**

### **Patient Model**

{  
  "id": "**********",  
  "personalDetails": {  
    "firstName": "string",  
    "lastName": "string",  
    "dateOfBirth": "YYYY-MM-DD",  
    "gender": "male|female|other",  
    "contactNumber": "string",  
    "email": "string"  
  },  
  "address": "string",  
  "emergencyContacts": \[\],  
  "medicalHistory": \[\],  
  "insurance": \[\],  
  "created\_on": "ISO-8601-date",  
  "updated\_on": "ISO-8601-date"  
}

### **Doctor Model**

{  
  "id": "doctor-id",  
  "general": {  
    "fullName": "string",  
    "designation": "string",  
    "department": "string",  
    "contactNumber": "string",  
    "workEmail": "string"  
  },  
  "professionalDetails": {  
    "medicalRegistration": {  
      "registrationNumber": "string",  
      "councilName": "string"  
    },  
    "qualifications": \[  
      {  
        "degree": "string",  
        "specialization": "string"  
      }  
    \]  
  },  
  "consultationFee": "number"  
}

### **Appointment Model**

{  
  "id": "APM20240725624",  
  "doctorId": "doctor-id",  
  "patientId": "patient-id",  
  "date": "YYYY-MM-DD",  
  "time": "HH:mm",  
  "department": "string",  
  "status": "scheduled|completed|cancelled",  
  "created\_on": "ISO-8601-date"  
}

### **Lab Test Model**

{  
  "LOINC\_NUM": "9999-4",  
  "COMPONENT": "R wave duration.lead AVL",  
  "PROPERTY": "Time",  
  "SYSTEM": "Heart",  
  "METHOD\_TYP": "EKG",  
  "CLASS": "EKG.MEAS",  
  "SHORTNAME": "R wave dur L-AVL",  
  "LONG\_COMMON\_NAME": "R wave duration in lead AVL",  
  "EXAMPLE\_UNITS": "ms",  
  "STATUS": "ACTIVE"  
}

### **Medicine Model**

{  
  "id": "medicine-id",  
  "name": "Medicine Name",  
  "genericName": "Generic Name",  
  "strength": "500mg",  
  "dosageForm": "Tablet",  
  "manufacturer": "Company Name",  
  "price": 10.50,  
  "isActive": **true**  
}

### **Lifestyle Model**

{  
  "source": "physical\_activity\_attitude",  
  "questions": \[  
    {  
      "id": "attitude",  
      "title": "Attitude",  
      "icon": "attitude",  
      "fields": \[  
        {  
          "id": "exercise\_preference",  
          "label": "Do you like doing exercise?",  
          "type": "radio",  
          "options": \["Yes", "No"\],  
          "required": **true**  
        }  
      \]  
    }  
  \]  
}

---

## **Error Codes**

| Status Code | Description |
| :---- | :---- |
| 400 | Bad Request \- Invalid input parameters |
| 401 | Unauthorized \- Missing or invalid authentication |
| 403 | Forbidden \- Insufficient permissions |
| 404 | Not Found \- Resource not found |
| 409 | Conflict \- Resource already exists |
| 500 | Internal Server Error \- Server-side error |

---

## **Pagination**

Most list endpoints support pagination with the following parameters: \- page: Page number (default: 1\) \- pageSize: Items per page (default: 10, max: 100\) \- continuationToken: For Cosmos DB pagination

**Response includes:** \- continuationToken: Token for next page \- hasMoreResults: Boolean indicating more data \- totalFetched: Number of items in current response \- currentPage: Current page number \- totalPages: Total number of pages (when available)

---

## **Authentication & Authorization**

### **JWT Token Structure**

{  
  "oid": "organization-id",  
  "uid": "user-id",  
  "role": "doctor",  
  "permissions": \["emr.access", "patient.view"\],  
  "exp": **********,  
  "iat": **********  
}

### **Permission Keys**

* emr.access \- Access EMR Module

* mrd.access \- Access MRD Module

* emr.patientinfo.view \- View Patient Info

* emr.patientinfo.edit \- Edit Patient Info

* emr.consultation.manage \- Manage Consultation

* emr.consultation.view \- View Consultation

* emr.consultation.create \- Create Consultation

* emr.consultation.edit \- Edit Consultation

* emr.prescription.view \- View Prescriptions

* emr.prescription.manage \- Manage Prescriptions

* emr.reports.manage \- Manage Reports

* emr.doctorprofile.view \- View Doctor Profile

* emr.doctorprofile.edit \- Edit Doctor Profile

* mrd.manage-patient.view \- View Patient Administrative Data

* mrd.manage-patient.edit \- Edit Patient Administrative Data

* mrd.patient-queue.manage \- Manage Patient Queue

* emr.lab-test.view \- View Lab Tests

* emr.lab-test.manage \- Manage Lab Tests

* emr.lab-test.search \- Search Lab Tests

* emr.test-package.manage \- Manage Test Packages

* emr.prescription-package.view \- View Prescription Packages

* emr.prescription-package.manage \- Manage Prescription Packages

* emr.medicine-package.view \- View Medicine Packages

* emr.medicine-package.manage \- Manage Medicine Packages

* emr.lifestyle.manage \- Manage Lifestyle

* dashboard.view \- View Dashboard

* role.manage \- Manage Roles

* permission.manage \- Manage Permissions

* organization.manage \- Manage Organizations

* organization.patients.view \- View Organization Patients

* user.view \- View User Information

* user.manage \- Manage Users

* payment.create \- Create Payment Orders

* payment.verify \- Verify Payments

* payment.view \- View Payment Details

* payment.stats \- View Payment Statistics

* payment.webhook \- Process Payment Webhooks

* mrd.payment.patient_registration \- Patient Registration Payment

* emr.payment.appointment_booking \- Appointment Booking Payment

* emr.payment.lab_test \- Lab Test Payment

* emr.payment.prescription \- Prescription Payment

* user.manage \- Manage users

* organization.view \- View organization details

* organization.manage \- Manage organization

* payment.webhook \- Process payment webhooks

* emr.lab-test.view \- View lab tests

* emr.lab-test.manage \- Manage lab tests

* emr.lab-test.search \- Search lab tests

* emr.prescription-package.view \- View prescription packages

* emr.prescription-package.manage \- Manage prescription packages

* emr.medicine-package.view \- View medicine packages

* emr.medicine-package.manage \- Manage medicine packages

* emr.lifestyle.manage \- Manage lifestyle

* emr.doctorprofile.view \- View doctor profile

* emr.doctorprofile.edit \- Edit doctor profile

* emr.patientinfo.view \- View patient info

* emr.patientinfo.edit \- Edit patient info

* emr.consultation.create \- Create consultation

* emr.consultation.edit \- Edit consultation

* emr.prescription.view \- View prescriptions

* emr.prescription.manage \- Manage prescriptions

* emr.reports.manage \- Manage reports

* emr.lab-test.view \- View lab tests

* emr.lab-test.manage \- Manage lab tests

* emr.lab-test.search \- Search lab tests

* emr.test-package.manage \- Manage test packages

* emr.prescription-package.view \- View prescription packages

* emr.prescription-package.manage \- Manage prescription packages

* emr.medicine-package.view \- View medicine packages

* emr.medicine-package.manage \- Manage medicine packages

* emr.lifestyle.manage \- Manage lifestyle

* mrd.payment.patient_registration \- Patient registration payment

* emr.payment.consultation \- Consultation payment

* emr.payment.lab_test \- Lab test payment

* emr.payment.prescription \- Prescription payment


---
