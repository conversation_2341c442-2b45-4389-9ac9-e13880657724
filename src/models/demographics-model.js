const CosmosDbMetadata = require('../models/CosmosDb-Metadata-model');

class DemographicsModel extends CosmosDbMetadata {
  constructor(data) {
    super(data);
    this.id = data.id || '';
    this.name = data.name || '';
    this.cmchId = data.cmchId || '';
    this.dob = data.dob || '';
    this.age = data.age || '';
    this.sex = data.sex || '';
    this.maritalStatus = data.maritalStatus || '';
    this.contacts = data.contacts || [];
    this.patientId = data.patientId || '';
  }

  // Validation method
  validate() {
    const errors = [];

    if (!this.name || this.name.trim() === '') {
      errors.push('Name is required');
    }

    if (!this.cmchId || this.cmchId.trim() === '') {
      errors.push('CMCH ID is required');
    }

    if (!this.dob || this.dob.trim() === '') {
      errors.push('Date of birth is required');
    }

    if (!this.age || this.age.trim() === '') {
      errors.push('Age is required');
    }

    if (!this.sex || !['male', 'female', 'other'].includes(this.sex.toLowerCase())) {
      errors.push('Sex must be male, female, or other');
    }

    if (this.maritalStatus && !['single', 'married', 'divorced', 'widowed'].includes(this.maritalStatus.toLowerCase())) {
      errors.push('Marital status must be single, married, divorced, or widowed');
    }

    if (Array.isArray(this.contacts) && this.contacts.length > 0) {
      this.contacts.forEach((contact, index) => {
        if (!contact.phone || contact.phone.trim() === '') {
          errors.push(`Contact ${index + 1} must have a phone number`);
        }
        // No backend email validation; UI is responsible
      });
    }

    // Validate date of birth is in the past
    if (this.dob) {
      const dobDate = new Date(this.dob);
      if (dobDate >= new Date()) {
        errors.push('Date of birth must be in the past');
      }
    }

    return errors;
  }

  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
}

module.exports = DemographicsModel;